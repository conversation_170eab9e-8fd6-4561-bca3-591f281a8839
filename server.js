const express = require("express");
const path = require("path");

const app = express();
const PORT = 8081;
const staticDir = "/var/www/bsod/";

process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
});

process.on('exit', (code) => {
  console.log('Process exiting with code:', code);
});

app.use((req, res, next) => {
  const requestedFile = req.path.replace(/^\/.*\//, '/'); // strip all leading folders
  const fullPath = path.join(staticDir, requestedFile);

  res.sendFile(fullPath, (err) => {
    if (err) {
      // if file does not exist, fallback to index.html (for SPA)
      res.sendFile(path.join(staticDir, "index.html"));
    }
  });
});

app.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}`);
});
