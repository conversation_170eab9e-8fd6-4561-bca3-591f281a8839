const express = require("express");
const path = require("path");
const fs = require("fs");
const { v4: uuidv4 } = require("uuid");
const winston = require("winston");
const cors = require("cors");

const app = express();
const PORT = 8081;
const staticDir = __dirname;

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, "user-logs");
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Configure Winston logger for user interactions
const userLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({
      filename: path.join(logsDir, 'user-interactions.log'),
      maxsize: 10485760, // 10MB
      maxFiles: 5
    }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// Store active sessions in memory (in production, use Redis or database)
const activeSessions = new Map();

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  userLogger.error('Uncaught Exception', { error: err.message, stack: err.stack });
});

process.on('exit', (code) => {
  console.log('Process exiting with code:', code);
  userLogger.info('Server shutting down', { exitCode: code });
});

// API Routes for user interaction logging

// Health check endpoint
app.get('/api/health', (req, res) => {
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    activeSessions: activeSessions.size,
    environment: {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch
    }
  };

  res.json(health);
});

// Create new session
app.post('/api/session/create', (req, res) => {
  const sessionId = uuidv4();
  const sessionData = {
    id: sessionId,
    startTime: new Date().toISOString(),
    userAgent: req.headers['user-agent'],
    ip: req.ip || req.connection.remoteAddress,
    interactions: []
  };

  activeSessions.set(sessionId, sessionData);

  userLogger.info('New session created', {
    sessionId,
    userAgent: sessionData.userAgent,
    ip: sessionData.ip,
    timestamp: sessionData.startTime
  });

  res.json({ sessionId });
});

// Log user interaction
app.post('/api/interaction/log', (req, res) => {
  const { sessionId, interaction } = req.body;

  if (!sessionId || !interaction) {
    return res.status(400).json({ error: 'Missing sessionId or interaction data' });
  }

  const session = activeSessions.get(sessionId);
  if (!session) {
    return res.status(404).json({ error: 'Session not found' });
  }

  // Add timestamp to interaction
  const timestampedInteraction = {
    ...interaction,
    timestamp: new Date().toISOString(),
    sessionId
  };

  // Store in session
  session.interactions.push(timestampedInteraction);

  // Log to file
  userLogger.info('User interaction', timestampedInteraction);

  res.json({ success: true });
});

// Batch log multiple interactions
app.post('/api/interaction/batch', (req, res) => {
  const { sessionId, interactions } = req.body;

  if (!sessionId || !Array.isArray(interactions)) {
    return res.status(400).json({ error: 'Missing sessionId or interactions array' });
  }

  const session = activeSessions.get(sessionId);
  if (!session) {
    return res.status(404).json({ error: 'Session not found' });
  }

  const timestamp = new Date().toISOString();

  interactions.forEach(interaction => {
    const timestampedInteraction = {
      ...interaction,
      timestamp,
      sessionId
    };

    session.interactions.push(timestampedInteraction);
    userLogger.info('User interaction (batch)', timestampedInteraction);
  });

  res.json({ success: true, logged: interactions.length });
});

// Get session data
app.get('/api/session/:sessionId', (req, res) => {
  const { sessionId } = req.params;
  const session = activeSessions.get(sessionId);

  if (!session) {
    return res.status(404).json({ error: 'Session not found' });
  }

  res.json(session);
});

// Get all active sessions
app.get('/api/sessions', (req, res) => {
  const sessions = Array.from(activeSessions.values()).map(session => ({
    id: session.id,
    startTime: session.startTime,
    userAgent: session.userAgent,
    ip: session.ip,
    interactionCount: session.interactions.length,
    lastActivity: session.interactions.length > 0
      ? session.interactions[session.interactions.length - 1].timestamp
      : session.startTime
  }));

  res.json(sessions);
});

// Get session analytics with escape attempts and detailed stats
app.get('/api/sys/analytics', (req, res) => {
  try {
    const logFile = path.join(logsDir, 'user-interactions.log');

    if (!fs.existsSync(logFile)) {
      return res.json({
        totalSessions: 0,
        activeSessions: 0,
        totalInteractions: 0,
        totalEscapeAttempts: 0,
        sessions: []
      });
    }

    // Read and parse log file
    const logContent = fs.readFileSync(logFile, 'utf8');
    const logLines = logContent.trim().split('\n').filter(line => line.trim());

    const sessionData = new Map();
    let totalInteractions = 0;
    let totalEscapeAttempts = 0;

    // Process each log entry
    for (const line of logLines) {
      try {
        const log = JSON.parse(line);

        if (log.sessionId && log.sessionId !== 'undefined') {
          if (!sessionData.has(log.sessionId)) {
            sessionData.set(log.sessionId, {
              id: log.sessionId,
              startTime: null,
              endTime: null,
              interactions: 0,
              escapeAttempts: 0,
              userAgent: null,
              ip: null,
              duration: null,
              isActive: false
            });
          }

          const session = sessionData.get(log.sessionId);

          // Track session creation
          if (log.message === 'New session created') {
            session.startTime = log.timestamp;
            session.userAgent = log.userAgent;
            session.ip = log.ip;
          }

          // Track session end
          if (log.message === 'Session ended') {
            session.endTime = log.timestamp;
            session.duration = log.duration;
          }

          // Count interactions
          if (log.message === 'User interaction (batch)' || log.message === 'User interaction') {
            session.interactions++;
            totalInteractions++;
          }

          // Count escape attempts
          if (log.type === 'escape_attempt') {
            session.escapeAttempts++;
            totalEscapeAttempts++;
          }
        }
      } catch (e) {
        // Skip invalid JSON lines
        continue;
      }
    }

    // Check which sessions are still active
    const activeSessions = Array.from(activeSessions.keys());
    sessionData.forEach((session, sessionId) => {
      session.isActive = activeSessions.includes(sessionId);
    });

    // Convert to array and sort by start time
    const sessions = Array.from(sessionData.values())
      .filter(session => session.startTime) // Only include sessions that were properly created
      .sort((a, b) => new Date(b.startTime) - new Date(a.startTime));

    const analytics = {
      totalSessions: sessions.length,
      activeSessions: sessions.filter(s => s.isActive).length,
      endedSessions: sessions.filter(s => !s.isActive).length,
      totalInteractions,
      totalEscapeAttempts,
      averageInteractionsPerSession: sessions.length > 0 ? Math.round(totalInteractions / sessions.length) : 0,
      averageEscapeAttemptsPerSession: sessions.length > 0 ? Math.round(totalEscapeAttempts / sessions.length) : 0,
      sessions: sessions.slice(0, 50) // Limit to last 50 sessions for performance
    };

    res.json(analytics);
  } catch (error) {
    console.error('Error generating analytics:', error);
    res.status(500).json({ error: 'Failed to generate analytics', details: error.message });
  }
});

// End session
app.post('/api/session/:sessionId/end', (req, res) => {
  const { sessionId } = req.params;
  const session = activeSessions.get(sessionId);

  if (!session) {
    return res.status(404).json({ error: 'Session not found' });
  }

  session.endTime = new Date().toISOString();

  userLogger.info('Session ended', {
    sessionId,
    startTime: session.startTime,
    endTime: session.endTime,
    totalInteractions: session.interactions.length,
    duration: new Date(session.endTime) - new Date(session.startTime)
  });

  // Remove from active sessions after logging
  activeSessions.delete(sessionId);

  res.json({ success: true });
});

// Admin interface to view logs (obfuscated endpoint)
app.get('/api/sys/telemetry/data', (req, res) => {
  const { sessionId, limit = 100 } = req.query;

  try {
    const logFile = path.join(logsDir, 'user-interactions.log');

    if (!fs.existsSync(logFile)) {
      return res.json({ logs: [], total: 0, message: 'No logs found' });
    }

    // Get file stats to handle large files
    const stats = fs.statSync(logFile);
    const maxFileSize = 50 * 1024 * 1024; // 50MB limit

    if (stats.size > maxFileSize) {
      // For very large files, read only the last part
      const fd = fs.openSync(logFile, 'r');
      const bufferSize = Math.min(1024 * 1024, stats.size); // Read last 1MB
      const buffer = Buffer.alloc(bufferSize);
      const position = Math.max(0, stats.size - bufferSize);

      fs.readSync(fd, buffer, 0, bufferSize, position);
      fs.closeSync(fd);

      const partialContent = buffer.toString('utf8');
      // Find the first complete line
      const firstNewline = partialContent.indexOf('\n');
      const logContent = firstNewline > -1 ? partialContent.substring(firstNewline + 1) : partialContent;

      const logLines = logContent.trim().split('\n').filter(line => line.trim());
      let logs = [];

      // Parse lines with error handling
      for (const line of logLines) {
        try {
          const log = JSON.parse(line);
          logs.push(log);
        } catch (e) {
          // Skip invalid JSON lines
          continue;
        }
      }

      // Filter by session if specified
      if (sessionId) {
        logs = logs.filter(log => log.sessionId === sessionId);
      }

      // Limit results
      const limitNum = parseInt(limit);
      logs = logs.slice(-limitNum);

      return res.json({
        logs,
        total: logs.length,
        message: `Showing last ${logs.length} entries from large log file (${(stats.size / 1024 / 1024).toFixed(1)}MB)`,
        truncated: true
      });
    }

    // For smaller files, read normally
    const logContent = fs.readFileSync(logFile, 'utf8');
    const logLines = logContent.trim().split('\n').filter(line => line.trim());

    let logs = [];
    let parseErrors = 0;

    // Parse lines with error counting
    for (const line of logLines) {
      try {
        const log = JSON.parse(line);
        logs.push(log);
      } catch (e) {
        parseErrors++;
        // Skip invalid JSON lines but count them
      }
    }

    // Filter by session if specified
    if (sessionId) {
      logs = logs.filter(log => log.sessionId === sessionId);
    }

    // Limit results
    const limitNum = parseInt(limit);
    const totalLogs = logs.length;
    logs = logs.slice(-limitNum);

    res.json({
      logs,
      total: totalLogs,
      showing: logs.length,
      parseErrors: parseErrors > 0 ? parseErrors : undefined,
      message: parseErrors > 0 ? `${parseErrors} lines could not be parsed` : undefined
    });

  } catch (error) {
    console.error('Error reading logs:', error);
    res.status(500).json({
      error: 'Failed to read logs',
      details: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Admin interface HTML (obfuscated endpoint)
app.get('/sys/dashboard', (req, res) => {
  const adminHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Interaction Logs - Admin</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px; }
        .sessions-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 15px; margin-bottom: 30px; }
        .session-card { border: 1px solid #ddd; padding: 15px; border-radius: 6px; background: #f9f9f9; position: relative; }
        .session-card.active { border-color: #28a745; background: #f8fff9; }
        .session-card.ended { border-color: #6c757d; background: #f8f9fa; opacity: 0.8; }
        .session-stats { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; margin: 10px 0; }
        .stat-box { background: white; padding: 8px; border-radius: 4px; text-align: center; border: 1px solid #e0e0e0; }
        .stat-number { font-size: 18px; font-weight: bold; color: #007bff; }
        .stat-label { font-size: 11px; color: #666; text-transform: uppercase; }
        .escape-attempts { color: #dc3545; }
        .interactions { color: #28a745; }
        .duration { color: #ffc107; }
        .summary-cards { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
        .summary-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .summary-card.danger { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); }
        .summary-card.success { background: linear-gradient(135deg, #51cf66 0%, #40c057 100%); }
        .summary-card.warning { background: linear-gradient(135deg, #ffd43b 0%, #fab005 100%); }
        .summary-number { font-size: 32px; font-weight: bold; margin-bottom: 5px; }
        .summary-label { font-size: 14px; opacity: 0.9; }
        .session-id { font-family: monospace; font-size: 12px; color: #666; }
        .logs-container { border: 1px solid #ddd; border-radius: 6px; max-height: 600px; overflow-y: auto; }
        .log-entry { padding: 10px; border-bottom: 1px solid #eee; font-family: monospace; font-size: 12px; }
        .log-entry:nth-child(even) { background: #f9f9f9; }
        .log-timestamp { color: #666; }
        .log-type { font-weight: bold; }
        .log-type.click { color: #007bff; }
        .log-type.keydown { color: #28a745; }
        .log-type.mousemove { color: #ffc107; }
        .log-type.page { color: #dc3545; }
        .controls { margin-bottom: 20px; }
        .controls input, .controls select, .controls button { margin: 5px; padding: 8px; }
        .refresh-btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
        .refresh-btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 User Interaction Logs</h1>
            <p>Real-time monitoring of user interactions on the BSOD page</p>
        </div>

        <div id="summaryContainer">
            <h3>📊 System Overview</h3>
            <div id="summaryCards" class="summary-cards"></div>
        </div>

        <div class="controls">
            <select id="sessionFilter">
                <option value="">All Sessions</option>
            </select>
            <input type="number" id="limitInput" placeholder="Limit (default: 100)" value="100">
            <button class="refresh-btn" onclick="refreshData()">🔄 Refresh</button>
            <button class="refresh-btn" onclick="toggleAutoRefresh()">⏱️ Auto Refresh: OFF</button>
            <div id="statusMessage" style="margin-left: 20px; color: #666; font-size: 14px;">Loading...</div>
        </div>

        <div id="sessionsContainer">
            <h3>Active Sessions</h3>
            <div id="sessionsGrid" class="sessions-grid"></div>
        </div>

        <div id="logsContainer">
            <h3>Interaction Logs</h3>
            <div id="logsDisplay" class="logs-container"></div>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let autoRefreshEnabled = false;

        async function fetchSessions() {
            try {
                const response = await fetch('/api/sessions');
                const sessions = await response.json();
                displaySessions(sessions);
                updateSessionFilter(sessions);
            } catch (error) {
                console.error('Failed to fetch sessions:', error);
            }
        }

        async function fetchAnalytics() {
            try {
                const response = await fetch('/api/sys/analytics');
                const analytics = await response.json();
                displaySummaryCards(analytics);
                displaySessionsWithStats(analytics.sessions);
                updateSessionFilter(analytics.sessions);
            } catch (error) {
                console.error('Failed to fetch analytics:', error);
            }
        }

        function displaySummaryCards(analytics) {
            const container = document.getElementById('summaryCards');
            container.innerHTML = \`
                <div class="summary-card">
                    <div class="summary-number">\${analytics.totalSessions}</div>
                    <div class="summary-label">Total Sessions</div>
                </div>
                <div class="summary-card success">
                    <div class="summary-number">\${analytics.activeSessions}</div>
                    <div class="summary-label">Active Sessions</div>
                </div>
                <div class="summary-card warning">
                    <div class="summary-number">\${analytics.totalInteractions.toLocaleString()}</div>
                    <div class="summary-label">Total Interactions</div>
                </div>
                <div class="summary-card danger">
                    <div class="summary-number">\${analytics.totalEscapeAttempts.toLocaleString()}</div>
                    <div class="summary-label">Escape Attempts</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number">\${analytics.averageInteractionsPerSession}</div>
                    <div class="summary-label">Avg Interactions/Session</div>
                </div>
                <div class="summary-card danger">
                    <div class="summary-number">\${analytics.averageEscapeAttemptsPerSession}</div>
                    <div class="summary-label">Avg Escapes/Session</div>
                </div>
            \`;
        }

        async function fetchLogs() {
            try {
                const sessionId = document.getElementById('sessionFilter').value;
                const limit = document.getElementById('limitInput').value || 100;
                const params = new URLSearchParams({ limit });
                if (sessionId) params.append('sessionId', sessionId);

                console.log('Fetching logs with params:', params.toString());
                const response = await fetch('/api/sys/telemetry/data?' + params);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Server response:', errorText);
                    throw new Error(\`HTTP error! status: \${response.status} - \${errorText}\`);
                }

                const data = await response.json();
                console.log('Received logs:', data);
                displayLogs(data.logs);

                // Update status with more details
                let statusMsg = \`Showing \${data.showing || data.logs.length} of \${data.total} logs\`;
                if (data.truncated) {
                    statusMsg += ' (large file - showing recent entries)';
                }
                if (data.parseErrors) {
                    statusMsg += \` - \${data.parseErrors} parse errors\`;
                }
                if (data.message) {
                    statusMsg += \` - \${data.message}\`;
                }
                document.getElementById('statusMessage').textContent = statusMsg;
            } catch (error) {
                console.error('Failed to fetch logs:', error);
                document.getElementById('statusMessage').textContent =
                    \`Error fetching logs: \${error.message}\`;
            }
        }

        function displaySessions(sessions) {
            const grid = document.getElementById('sessionsGrid');
            grid.innerHTML = sessions.map(session => \`
                <div class="session-card active">
                    <div class="session-id">Session: \${session.id}</div>
                    <div><strong>Started:</strong> \${new Date(session.startTime).toLocaleString()}</div>
                    <div><strong>User Agent:</strong> \${session.userAgent.substring(0, 50)}...</div>
                    <div><strong>IP:</strong> \${session.ip}</div>
                    <div><strong>Interactions:</strong> \${session.interactionCount}</div>
                    <div><strong>Last Activity:</strong> \${new Date(session.lastActivity).toLocaleString()}</div>
                </div>
            \`).join('');
        }

        function updateSessionFilter(sessions) {
            const select = document.getElementById('sessionFilter');
            const currentValue = select.value;
            select.innerHTML = '<option value="">All Sessions</option>' +
                sessions.map(session => \`<option value="\${session.id}">\${session.id.substring(0, 8)}... (\${session.interactionCount} interactions)</option>\`).join('');
            select.value = currentValue;
        }

        function displayLogs(logs) {
            const container = document.getElementById('logsDisplay');

            if (!logs || logs.length === 0) {
                container.innerHTML = '<div class="log-entry" style="text-align: center; color: #666;">No logs found. Try refreshing or check if there are active sessions.</div>';
                return;
            }

            container.innerHTML = logs.map(log => {
                const timestamp = new Date(log.timestamp).toLocaleString();
                const type = log.type || log.message || 'unknown';
                return \`
                    <div class="log-entry">
                        <span class="log-timestamp">\${timestamp}</span>
                        <span class="log-type \${type}">\${type.toUpperCase()}</span>
                        <span class="session-id">[\${log.sessionId ? log.sessionId.substring(0, 8) : 'N/A'}]</span>
                        \${formatLogMessage(log)}
                    </div>
                \`;
            }).join('');
            container.scrollTop = container.scrollHeight;
        }

        function formatLogMessage(log) {
            switch(log.type) {
                case 'click':
                    const elementInfo = log.element ?
                        (log.element.tagName || log.element) : 'unknown';
                    return \`Click on \${elementInfo} at (\${log.x}, \${log.y})\`;
                case 'keydown':
                case 'keyup':
                    return \`Key \${log.type}: \${log.key} (code: \${log.code || 'N/A'})\`;
                case 'mousemove':
                    return \`Mouse moved to (\${log.x}, \${log.y})\`;
                case 'mouseenter':
                case 'mouseleave':
                    const element = log.element ?
                        (log.element.tagName || 'unknown') : 'unknown';
                    return \`Mouse \${log.type.replace('mouse', '')} \${element}\`;
                case 'page':
                    return \`Page event: \${log.event}\`;
                case 'fullscreen':
                    return \`Fullscreen: \${log.action}\`;
                case 'audio':
                    return \`Audio: \${log.action}\`;
                case 'button':
                    return \`Button action: \${log.action} on \${log.element}\`;
                case 'escape_attempt':
                    return \`Escape attempt: \${log.key} (blocked: \${log.blocked})\`;
                case 'window':
                    return \`Window \${log.event}: \${log.viewportSize || ''}\`;
                case 'focus':
                case 'blur':
                    return \`Element \${log.type}: \${log.element ? log.element.tagName : 'unknown'}\`;
                case 'scroll':
                    return \`Scroll to (\${log.scrollX}, \${log.scrollY})\`;
                default:
                    if (log.message && log.message !== 'User interaction (batch)') {
                        return log.message;
                    }
                    return JSON.stringify(log).substring(0, 100) + '...';
            }
        }

        function refreshData() {
            fetchSessions();
            fetchLogs();
        }

        function toggleAutoRefresh() {
            const button = document.querySelector('button[onclick="toggleAutoRefresh()"]');
            if (autoRefreshEnabled) {
                clearInterval(autoRefreshInterval);
                autoRefreshEnabled = false;
                button.textContent = '⏱️ Auto Refresh: OFF';
            } else {
                autoRefreshInterval = setInterval(refreshData, 2000);
                autoRefreshEnabled = true;
                button.textContent = '⏱️ Auto Refresh: ON';
            }
        }

        // Event listeners
        document.getElementById('sessionFilter').addEventListener('change', fetchLogs);
        document.getElementById('limitInput').addEventListener('change', fetchLogs);

        // Initial load
        refreshData();
    </script>
</body>
</html>`;

  res.send(adminHtml);
});

// Static file serving (keep original functionality)
app.use((req, res, next) => {
  const requestedFile = req.path.replace(/^\/.*\//, '/'); // strip all leading folders
  const fullPath = path.join(staticDir, requestedFile);

  res.sendFile(fullPath, (err) => {
    if (err) {
      // if file does not exist, fallback to index.html (for SPA)
      res.sendFile(path.join(staticDir, "index.html"));
    }
  });
});

app.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}`);
  console.log(`System dashboard available at http://localhost:${PORT}/sys/dashboard`);
  userLogger.info('Server started', { port: PORT });
});
