#!/usr/bin/env node

// Script to diagnose and test Winston logging issues
const fs = require('fs');
const path = require('path');
const winston = require('winston');

console.log('🔍 Diagnosing Winston logging issues...\n');

// Check if user-logs directory exists
const logsDir = path.join(__dirname, 'user-logs');
console.log('📁 Log directory:', logsDir);

if (!fs.existsSync(logsDir)) {
    console.log('❌ Log directory does not exist. Creating...');
    fs.mkdirSync(logsDir, { recursive: true });
    console.log('✅ Log directory created');
} else {
    console.log('✅ Log directory exists');
}

// Check permissions
try {
    const stats = fs.statSync(logsDir);
    console.log('📋 Directory permissions:', stats.mode.toString(8));
    
    // Test write access
    const testFile = path.join(logsDir, 'test-write.tmp');
    fs.writeFileSync(testFile, 'test');
    fs.unlinkSync(testFile);
    console.log('✅ Directory is writable');
} catch (error) {
    console.log('❌ Directory permission issue:', error.message);
}

// Check existing log file
const logFile = path.join(logsDir, 'user-interactions.log');
if (fs.existsSync(logFile)) {
    const stats = fs.statSync(logFile);
    console.log('📄 Log file size:', stats.size, 'bytes');
    console.log('📅 Last modified:', stats.mtime);
    
    // Show last few lines
    try {
        const content = fs.readFileSync(logFile, 'utf8');
        const lines = content.trim().split('\n');
        console.log('📝 Last log entry:');
        if (lines.length > 0 && lines[lines.length - 1]) {
            const lastEntry = JSON.parse(lines[lines.length - 1]);
            console.log('   Timestamp:', lastEntry.timestamp);
            console.log('   Message:', lastEntry.message);
            console.log('   Session ID:', lastEntry.sessionId || 'N/A');
        }
    } catch (error) {
        console.log('⚠️  Could not parse last log entry:', error.message);
    }
} else {
    console.log('📄 Log file does not exist yet');
}

// Test Winston logger
console.log('\n🧪 Testing Winston logger...');

try {
    const testLogger = winston.createLogger({
        level: 'info',
        format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
        ),
        transports: [
            new winston.transports.File({ 
                filename: logFile,
                maxsize: 10485760,
                maxFiles: 5
            }),
            new winston.transports.Console({
                format: winston.format.simple()
            })
        ],
        exitOnError: false
    });

    // Test logging
    const testData = {
        message: 'Test log from diagnostic script',
        timestamp: new Date().toISOString(),
        test: true,
        sessionId: 'diagnostic-test'
    };

    testLogger.info('Test log from diagnostic script', testData);
    
    // Wait a moment for async write
    setTimeout(() => {
        console.log('✅ Winston test completed');
        
        // Check if the test entry was written
        if (fs.existsSync(logFile)) {
            const content = fs.readFileSync(logFile, 'utf8');
            if (content.includes('diagnostic-test')) {
                console.log('✅ Test entry found in log file');
            } else {
                console.log('❌ Test entry not found in log file');
            }
        }
        
        console.log('\n📋 Diagnostic Summary:');
        console.log('- Log directory: ' + (fs.existsSync(logsDir) ? '✅' : '❌'));
        console.log('- Write permissions: ✅');
        console.log('- Winston logger: ✅');
        console.log('\n💡 If logs still not appearing:');
        console.log('1. Restart the application: pm2 restart app-8081');
        console.log('2. Check PM2 logs: pm2 logs app-8081');
        console.log('3. Monitor log file: tail -f user-logs/user-interactions.log');
        
        process.exit(0);
    }, 1000);

} catch (error) {
    console.log('❌ Winston test failed:', error.message);
    console.log('\n🔧 Possible fixes:');
    console.log('1. npm install winston');
    console.log('2. Check file permissions');
    console.log('3. Restart the application');
    process.exit(1);
}
