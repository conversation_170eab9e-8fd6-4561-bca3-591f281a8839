{
  "lockfileVersion": 1,
  "workspaces": {
    "": {
      "name": "pm2",
      "dependencies": {
        "@pm2/agent": "~2.1.1",
        "@pm2/io": "~6.1.0",
        "@pm2/js-api": "~0.8.0",
        "@pm2/pm2-version-check": "latest",
        "ansis": "4.0.0-node10",
        "async": "~3.2.6",
        "blessed": "0.1.81",
        "chokidar": "^3.5.3",
        "cli-tableau": "^2.0.0",
        "commander": "2.15.1",
        "croner": "~4.1.92",
        "dayjs": "~1.11.13",
        "debug": "^4.3.7",
        "enquirer": "2.3.6",
        "eventemitter2": "5.0.1",
        "fclone": "1.0.11",
        "js-yaml": "~4.1.0",
        "mkdirp": "1.0.4",
        "needle": "2.4.0",
        "pidusage": "~3.0",
        "pm2-axon": "~4.0.1",
        "pm2-axon-rpc": "~0.7.1",
        "pm2-deploy": "~1.0.2",
        "pm2-multimeter": "^0.1.2",
        "promptly": "^2",
        "semver": "^7.6.2",
        "source-map-support": "0.5.21",
        "sprintf-js": "1.1.2",
        "vizion": "~2.2.1",
      },
      "devDependencies": {
        "mocha": "^10.8.0",
        "should": "^13.2.3",
      },
      "optionalDependencies": {
        "pm2-sysmonit": "^1.2.8",
      },
    },
  },
  "packages": {
    "@pm2/agent": ["@pm2/agent@2.1.1", "", { "dependencies": { "async": "~3.2.0", "chalk": "~3.0.0", "dayjs": "~1.8.24", "debug": "~4.3.1", "eventemitter2": "~5.0.1", "fast-json-patch": "^3.1.0", "fclone": "~1.0.11", "pm2-axon": "~4.0.1", "pm2-axon-rpc": "~0.7.0", "proxy-agent": "~6.4.0", "semver": "~7.5.0", "ws": "~7.5.10" } }, ""],

    "@pm2/io": ["@pm2/io@6.1.0", "", { "dependencies": { "async": "~2.6.1", "debug": "~4.3.1", "eventemitter2": "^6.3.1", "require-in-the-middle": "^5.0.0", "semver": "~7.5.4", "shimmer": "^1.2.0", "signal-exit": "^3.0.3", "tslib": "1.9.3" } }, ""],

    "@pm2/js-api": ["@pm2/js-api@0.8.0", "", { "dependencies": { "async": "^2.6.3", "debug": "~4.3.1", "eventemitter2": "^6.3.1", "extrareqp2": "^1.0.0", "ws": "^7.0.0" } }, ""],

    "@pm2/pm2-version-check": ["@pm2/pm2-version-check@1.0.4", "", { "dependencies": { "debug": "^4.3.1" } }, "sha512-SXsM27SGH3yTWKc2fKR4SYNxsmnvuBQ9dd6QHtEWmiZ/VqaOYPAIlS8+vMcn27YLtAEBGvNRSh3TPNvtjZgfqA=="],

    "@tootallnate/quickjs-emscripten": ["@tootallnate/quickjs-emscripten@0.23.0", "", {}, ""],

    "agent-base": ["agent-base@7.1.3", "", {}, ""],

    "amp": ["amp@0.3.1", "", {}, ""],

    "amp-message": ["amp-message@0.1.2", "", { "dependencies": { "amp": "0.3.1" } }, ""],

    "ansi-colors": ["ansi-colors@4.1.3", "", {}, ""],

    "ansi-regex": ["ansi-regex@5.0.1", "", {}, ""],

    "ansi-styles": ["ansi-styles@4.3.0", "", { "dependencies": { "color-convert": "^2.0.1" } }, ""],

    "ansis": ["ansis@4.0.0-node10", "", {}, ""],

    "anymatch": ["anymatch@3.1.3", "", { "dependencies": { "normalize-path": "^3.0.0", "picomatch": "^2.0.4" } }, ""],

    "argparse": ["argparse@2.0.1", "", {}, ""],

    "ast-types": ["ast-types@0.13.4", "", { "dependencies": { "tslib": "^2.0.1" } }, ""],

    "async": ["async@3.2.6", "", {}, ""],

    "balanced-match": ["balanced-match@1.0.2", "", {}, ""],

    "basic-ftp": ["basic-ftp@5.0.5", "", {}, ""],

    "binary-extensions": ["binary-extensions@2.3.0", "", {}, ""],

    "blessed": ["blessed@0.1.81", "", { "bin": "bin/tput.js" }, ""],

    "bodec": ["bodec@0.1.0", "", {}, ""],

    "brace-expansion": ["brace-expansion@2.0.1", "", { "dependencies": { "balanced-match": "^1.0.0" } }, ""],

    "braces": ["braces@3.0.3", "", { "dependencies": { "fill-range": "^7.1.1" } }, ""],

    "browser-stdout": ["browser-stdout@1.3.1", "", {}, ""],

    "buffer-from": ["buffer-from@1.1.2", "", {}, ""],

    "camelcase": ["camelcase@6.3.0", "", {}, ""],

    "chalk": ["chalk@3.0.0", "", { "dependencies": { "ansi-styles": "^4.1.0", "supports-color": "^7.1.0" } }, ""],

    "charm": ["charm@0.1.2", "", {}, ""],

    "chokidar": ["chokidar@3.6.0", "", { "dependencies": { "anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0" }, "optionalDependencies": { "fsevents": "~2.3.2" } }, ""],

    "cli-tableau": ["cli-tableau@2.0.1", "", { "dependencies": { "chalk": "3.0.0" } }, ""],

    "cliui": ["cliui@7.0.4", "", { "dependencies": { "string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0" } }, ""],

    "color-convert": ["color-convert@2.0.1", "", { "dependencies": { "color-name": "~1.1.4" } }, ""],

    "color-name": ["color-name@1.1.4", "", {}, ""],

    "commander": ["commander@2.15.1", "", {}, ""],

    "croner": ["croner@4.1.97", "", {}, ""],

    "culvert": ["culvert@0.1.2", "", {}, ""],

    "data-uri-to-buffer": ["data-uri-to-buffer@6.0.2", "", {}, ""],

    "dayjs": ["dayjs@1.11.13", "", {}, ""],

    "debug": ["debug@4.4.1", "", { "dependencies": { "ms": "^2.1.3" } }, ""],

    "decamelize": ["decamelize@4.0.0", "", {}, ""],

    "degenerator": ["degenerator@5.0.1", "", { "dependencies": { "ast-types": "^0.13.4", "escodegen": "^2.1.0", "esprima": "^4.0.1" } }, ""],

    "diff": ["diff@5.2.0", "", {}, ""],

    "emoji-regex": ["emoji-regex@8.0.0", "", {}, ""],

    "enquirer": ["enquirer@2.3.6", "", { "dependencies": { "ansi-colors": "^4.1.1" } }, ""],

    "escalade": ["escalade@3.2.0", "", {}, ""],

    "escape-string-regexp": ["escape-string-regexp@4.0.0", "", {}, ""],

    "escodegen": ["escodegen@2.1.0", "", { "dependencies": { "esprima": "^4.0.1", "estraverse": "^5.2.0", "esutils": "^2.0.2" }, "optionalDependencies": { "source-map": "~0.6.1" }, "bin": { "escodegen": "bin/escodegen.js", "esgenerate": "bin/esgenerate.js" } }, ""],

    "esprima": ["esprima@4.0.1", "", { "bin": { "esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js" } }, ""],

    "estraverse": ["estraverse@5.3.0", "", {}, ""],

    "esutils": ["esutils@2.0.3", "", {}, ""],

    "eventemitter2": ["eventemitter2@5.0.1", "", {}, ""],

    "extrareqp2": ["extrareqp2@1.0.0", "", { "dependencies": { "follow-redirects": "^1.14.0" } }, ""],

    "fast-json-patch": ["fast-json-patch@3.1.1", "", {}, ""],

    "fclone": ["fclone@1.0.11", "", {}, ""],

    "fill-range": ["fill-range@7.1.1", "", { "dependencies": { "to-regex-range": "^5.0.1" } }, ""],

    "find-up": ["find-up@5.0.0", "", { "dependencies": { "locate-path": "^6.0.0", "path-exists": "^4.0.0" } }, ""],

    "flat": ["flat@5.0.2", "", { "bin": "cli.js" }, ""],

    "follow-redirects": ["follow-redirects@1.15.9", "", {}, ""],

    "fs.realpath": ["fs.realpath@1.0.0", "", {}, ""],

    "fsevents": ["fsevents@2.3.3", "", { "os": "darwin" }, ""],

    "function-bind": ["function-bind@1.1.2", "", {}, ""],

    "get-caller-file": ["get-caller-file@2.0.5", "", {}, ""],

    "get-uri": ["get-uri@6.0.4", "", { "dependencies": { "basic-ftp": "^5.0.2", "data-uri-to-buffer": "^6.0.2", "debug": "^4.3.4" } }, ""],

    "git-node-fs": ["git-node-fs@1.0.0", "", {}, ""],

    "git-sha1": ["git-sha1@0.1.2", "", {}, ""],

    "glob": ["glob@8.1.0", "", { "dependencies": { "fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^5.0.1", "once": "^1.3.0" } }, ""],

    "glob-parent": ["glob-parent@5.1.2", "", { "dependencies": { "is-glob": "^4.0.1" } }, ""],

    "has-flag": ["has-flag@4.0.0", "", {}, ""],

    "hasown": ["hasown@2.0.2", "", { "dependencies": { "function-bind": "^1.1.2" } }, ""],

    "he": ["he@1.2.0", "", { "bin": "bin/he" }, ""],

    "http-proxy-agent": ["http-proxy-agent@7.0.2", "", { "dependencies": { "agent-base": "^7.1.0", "debug": "^4.3.4" } }, ""],

    "https-proxy-agent": ["https-proxy-agent@7.0.6", "", { "dependencies": { "agent-base": "^7.1.2", "debug": "4" } }, ""],

    "iconv-lite": ["iconv-lite@0.4.24", "", { "dependencies": { "safer-buffer": ">= 2.1.2 < 3" } }, ""],

    "inflight": ["inflight@1.0.6", "", { "dependencies": { "once": "^1.3.0", "wrappy": "1" } }, ""],

    "inherits": ["inherits@2.0.4", "", {}, ""],

    "ini": ["ini@1.3.8", "", {}, ""],

    "ip-address": ["ip-address@9.0.5", "", { "dependencies": { "jsbn": "1.1.0", "sprintf-js": "^1.1.3" } }, ""],

    "is-binary-path": ["is-binary-path@2.1.0", "", { "dependencies": { "binary-extensions": "^2.0.0" } }, ""],

    "is-core-module": ["is-core-module@2.16.1", "", { "dependencies": { "hasown": "^2.0.2" } }, ""],

    "is-extglob": ["is-extglob@2.1.1", "", {}, ""],

    "is-fullwidth-code-point": ["is-fullwidth-code-point@3.0.0", "", {}, ""],

    "is-glob": ["is-glob@4.0.3", "", { "dependencies": { "is-extglob": "^2.1.1" } }, ""],

    "is-number": ["is-number@7.0.0", "", {}, ""],

    "is-plain-obj": ["is-plain-obj@2.1.0", "", {}, ""],

    "is-unicode-supported": ["is-unicode-supported@0.1.0", "", {}, ""],

    "js-git": ["js-git@0.7.8", "", { "dependencies": { "bodec": "^0.1.0", "culvert": "^0.1.2", "git-sha1": "^0.1.2", "pako": "^0.2.5" } }, ""],

    "js-yaml": ["js-yaml@4.1.0", "", { "dependencies": { "argparse": "^2.0.1" }, "bin": "bin/js-yaml.js" }, ""],

    "jsbn": ["jsbn@1.1.0", "", {}, ""],

    "json-stringify-safe": ["json-stringify-safe@5.0.1", "", {}, ""],

    "locate-path": ["locate-path@6.0.0", "", { "dependencies": { "p-locate": "^5.0.0" } }, ""],

    "lodash": ["lodash@4.17.21", "", {}, ""],

    "log-symbols": ["log-symbols@4.1.0", "", { "dependencies": { "chalk": "^4.1.0", "is-unicode-supported": "^0.1.0" } }, ""],

    "lru-cache": ["lru-cache@7.18.3", "", {}, ""],

    "minimatch": ["minimatch@5.1.6", "", { "dependencies": { "brace-expansion": "^2.0.1" } }, ""],

    "mkdirp": ["mkdirp@1.0.4", "", { "bin": "bin/cmd.js" }, ""],

    "mocha": ["mocha@10.8.2", "", { "dependencies": { "ansi-colors": "^4.1.3", "browser-stdout": "^1.3.1", "chokidar": "^3.5.3", "debug": "^4.3.5", "diff": "^5.2.0", "escape-string-regexp": "^4.0.0", "find-up": "^5.0.0", "glob": "^8.1.0", "he": "^1.2.0", "js-yaml": "^4.1.0", "log-symbols": "^4.1.0", "minimatch": "^5.1.6", "ms": "^2.1.3", "serialize-javascript": "^6.0.2", "strip-json-comments": "^3.1.1", "supports-color": "^8.1.1", "workerpool": "^6.5.1", "yargs": "^16.2.0", "yargs-parser": "^20.2.9", "yargs-unparser": "^2.0.0" }, "bin": { "_mocha": "bin/_mocha", "mocha": "bin/mocha.js" } }, ""],

    "module-details-from-path": ["module-details-from-path@1.0.4", "", {}, ""],

    "ms": ["ms@2.1.3", "", {}, ""],

    "mute-stream": ["mute-stream@0.0.8", "", {}, ""],

    "needle": ["needle@2.4.0", "", { "dependencies": { "debug": "^3.2.6", "iconv-lite": "^0.4.4", "sax": "^1.2.4" }, "bin": "bin/needle" }, ""],

    "netmask": ["netmask@2.0.2", "", {}, ""],

    "normalize-path": ["normalize-path@3.0.0", "", {}, ""],

    "once": ["once@1.4.0", "", { "dependencies": { "wrappy": "1" } }, ""],

    "p-limit": ["p-limit@3.1.0", "", { "dependencies": { "yocto-queue": "^0.1.0" } }, ""],

    "p-locate": ["p-locate@5.0.0", "", { "dependencies": { "p-limit": "^3.0.2" } }, ""],

    "pac-proxy-agent": ["pac-proxy-agent@7.2.0", "", { "dependencies": { "@tootallnate/quickjs-emscripten": "^0.23.0", "agent-base": "^7.1.2", "debug": "^4.3.4", "get-uri": "^6.0.1", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.6", "pac-resolver": "^7.0.1", "socks-proxy-agent": "^8.0.5" } }, ""],

    "pac-resolver": ["pac-resolver@7.0.1", "", { "dependencies": { "degenerator": "^5.0.0", "netmask": "^2.0.2" } }, ""],

    "pako": ["pako@0.2.9", "", {}, ""],

    "path-exists": ["path-exists@4.0.0", "", {}, ""],

    "path-parse": ["path-parse@1.0.7", "", {}, ""],

    "picomatch": ["picomatch@2.3.1", "", {}, ""],

    "pidusage": ["pidusage@3.0.2", "", { "dependencies": { "safe-buffer": "^5.2.1" } }, ""],

    "pm2-axon": ["pm2-axon@4.0.1", "", { "dependencies": { "amp": "~0.3.1", "amp-message": "~0.1.1", "debug": "^4.3.1", "escape-string-regexp": "^4.0.0" } }, ""],

    "pm2-axon-rpc": ["pm2-axon-rpc@0.7.1", "", { "dependencies": { "debug": "^4.3.1" } }, ""],

    "pm2-deploy": ["pm2-deploy@1.0.2", "", { "dependencies": { "run-series": "^1.1.8", "tv4": "^1.3.0" } }, ""],

    "pm2-multimeter": ["pm2-multimeter@0.1.2", "", { "dependencies": { "charm": "~0.1.1" } }, ""],

    "pm2-sysmonit": ["pm2-sysmonit@1.2.8", "", { "dependencies": { "async": "^3.2.0", "debug": "^4.3.1", "pidusage": "^2.0.21", "systeminformation": "^5.7", "tx2": "~1.0.4" } }, ""],

    "promptly": ["promptly@2.2.0", "", { "dependencies": { "read": "^1.0.4" } }, ""],

    "proxy-agent": ["proxy-agent@6.4.0", "", { "dependencies": { "agent-base": "^7.0.2", "debug": "^4.3.4", "http-proxy-agent": "^7.0.1", "https-proxy-agent": "^7.0.3", "lru-cache": "^7.14.1", "pac-proxy-agent": "^7.0.1", "proxy-from-env": "^1.1.0", "socks-proxy-agent": "^8.0.2" } }, ""],

    "proxy-from-env": ["proxy-from-env@1.1.0", "", {}, ""],

    "randombytes": ["randombytes@2.1.0", "", { "dependencies": { "safe-buffer": "^5.1.0" } }, ""],

    "read": ["read@1.0.7", "", { "dependencies": { "mute-stream": "~0.0.4" } }, ""],

    "readdirp": ["readdirp@3.6.0", "", { "dependencies": { "picomatch": "^2.2.1" } }, ""],

    "require-directory": ["require-directory@2.1.1", "", {}, ""],

    "require-in-the-middle": ["require-in-the-middle@5.2.0", "", { "dependencies": { "debug": "^4.1.1", "module-details-from-path": "^1.0.3", "resolve": "^1.22.1" } }, ""],

    "resolve": ["resolve@1.22.10", "", { "dependencies": { "is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0" }, "bin": "bin/resolve" }, ""],

    "run-series": ["run-series@1.1.9", "", {}, ""],

    "safe-buffer": ["safe-buffer@5.2.1", "", {}, ""],

    "safer-buffer": ["safer-buffer@2.1.2", "", {}, ""],

    "sax": ["sax@1.4.1", "", {}, ""],

    "semver": ["semver@7.7.2", "", { "bin": "bin/semver.js" }, ""],

    "serialize-javascript": ["serialize-javascript@6.0.2", "", { "dependencies": { "randombytes": "^2.1.0" } }, ""],

    "shimmer": ["shimmer@1.2.1", "", {}, ""],

    "should": ["should@13.2.3", "", { "dependencies": { "should-equal": "^2.0.0", "should-format": "^3.0.3", "should-type": "^1.4.0", "should-type-adaptors": "^1.0.1", "should-util": "^1.0.0" } }, ""],

    "should-equal": ["should-equal@2.0.0", "", { "dependencies": { "should-type": "^1.4.0" } }, ""],

    "should-format": ["should-format@3.0.3", "", { "dependencies": { "should-type": "^1.3.0", "should-type-adaptors": "^1.0.1" } }, ""],

    "should-type": ["should-type@1.4.0", "", {}, ""],

    "should-type-adaptors": ["should-type-adaptors@1.1.0", "", { "dependencies": { "should-type": "^1.3.0", "should-util": "^1.0.0" } }, ""],

    "should-util": ["should-util@1.0.1", "", {}, ""],

    "signal-exit": ["signal-exit@3.0.7", "", {}, ""],

    "smart-buffer": ["smart-buffer@4.2.0", "", {}, ""],

    "socks": ["socks@2.8.4", "", { "dependencies": { "ip-address": "^9.0.5", "smart-buffer": "^4.2.0" } }, ""],

    "socks-proxy-agent": ["socks-proxy-agent@8.0.5", "", { "dependencies": { "agent-base": "^7.1.2", "debug": "^4.3.4", "socks": "^2.8.3" } }, ""],

    "source-map": ["source-map@0.6.1", "", {}, ""],

    "source-map-support": ["source-map-support@0.5.21", "", { "dependencies": { "buffer-from": "^1.0.0", "source-map": "^0.6.0" } }, ""],

    "sprintf-js": ["sprintf-js@1.1.2", "", {}, ""],

    "string-width": ["string-width@4.2.3", "", { "dependencies": { "emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1" } }, ""],

    "strip-ansi": ["strip-ansi@6.0.1", "", { "dependencies": { "ansi-regex": "^5.0.1" } }, ""],

    "strip-json-comments": ["strip-json-comments@3.1.1", "", {}, ""],

    "supports-color": ["supports-color@8.1.1", "", { "dependencies": { "has-flag": "^4.0.0" } }, ""],

    "supports-preserve-symlinks-flag": ["supports-preserve-symlinks-flag@1.0.0", "", {}, ""],

    "systeminformation": ["systeminformation@5.25.11", "", { "os": "!aix", "bin": "lib/cli.js" }, ""],

    "to-regex-range": ["to-regex-range@5.0.1", "", { "dependencies": { "is-number": "^7.0.0" } }, ""],

    "tslib": ["tslib@1.9.3", "", {}, ""],

    "tv4": ["tv4@1.3.0", "", {}, ""],

    "tx2": ["tx2@1.0.5", "", { "dependencies": { "json-stringify-safe": "^5.0.1" } }, ""],

    "vizion": ["vizion@2.2.1", "", { "dependencies": { "async": "^2.6.3", "git-node-fs": "^1.0.0", "ini": "^1.3.5", "js-git": "^0.7.8" } }, ""],

    "workerpool": ["workerpool@6.5.1", "", {}, ""],

    "wrap-ansi": ["wrap-ansi@7.0.0", "", { "dependencies": { "ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0" } }, ""],

    "wrappy": ["wrappy@1.0.2", "", {}, ""],

    "ws": ["ws@7.5.10", "", { "peerDependencies": { "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2" }, "optionalPeers": ["bufferutil", "utf-8-validate"] }, ""],

    "y18n": ["y18n@5.0.8", "", {}, ""],

    "yallist": ["yallist@4.0.0", "", {}, ""],

    "yargs": ["yargs@16.2.0", "", { "dependencies": { "cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2" } }, ""],

    "yargs-parser": ["yargs-parser@20.2.9", "", {}, ""],

    "yargs-unparser": ["yargs-unparser@2.0.0", "", { "dependencies": { "camelcase": "^6.0.0", "decamelize": "^4.0.0", "flat": "^5.0.2", "is-plain-obj": "^2.1.0" } }, ""],

    "yocto-queue": ["yocto-queue@0.1.0", "", {}, ""],

    "@pm2/agent/dayjs": ["dayjs@1.8.36", "", {}, ""],

    "@pm2/agent/debug": ["debug@4.3.7", "", { "dependencies": { "ms": "^2.1.3" } }, ""],

    "@pm2/agent/semver": ["semver@7.5.4", "", { "dependencies": { "lru-cache": "^6.0.0" }, "bin": "bin/semver.js" }, ""],

    "@pm2/io/async": ["async@2.6.4", "", { "dependencies": { "lodash": "^4.17.14" } }, ""],

    "@pm2/io/debug": ["debug@4.3.7", "", { "dependencies": { "ms": "^2.1.3" } }, ""],

    "@pm2/io/eventemitter2": ["eventemitter2@6.4.9", "", {}, ""],

    "@pm2/io/semver": ["semver@7.5.4", "", { "dependencies": { "lru-cache": "^6.0.0" }, "bin": "bin/semver.js" }, ""],

    "@pm2/js-api/async": ["async@2.6.4", "", { "dependencies": { "lodash": "^4.17.14" } }, ""],

    "@pm2/js-api/debug": ["debug@4.3.7", "", { "dependencies": { "ms": "^2.1.3" } }, ""],

    "@pm2/js-api/eventemitter2": ["eventemitter2@6.4.9", "", {}, ""],

    "ast-types/tslib": ["tslib@2.8.1", "", {}, ""],

    "chalk/supports-color": ["supports-color@7.2.0", "", { "dependencies": { "has-flag": "^4.0.0" } }, ""],

    "ip-address/sprintf-js": ["sprintf-js@1.1.3", "", {}, ""],

    "log-symbols/chalk": ["chalk@4.1.2", "", { "dependencies": { "ansi-styles": "^4.1.0", "supports-color": "^7.1.0" } }, ""],

    "needle/debug": ["debug@3.2.7", "", { "dependencies": { "ms": "^2.1.1" } }, ""],

    "pm2-sysmonit/pidusage": ["pidusage@2.0.21", "", { "dependencies": { "safe-buffer": "^5.2.1" } }, ""],

    "vizion/async": ["async@2.6.4", "", { "dependencies": { "lodash": "^4.17.14" } }, ""],

    "@pm2/agent/semver/lru-cache": ["lru-cache@6.0.0", "", { "dependencies": { "yallist": "^4.0.0" } }, ""],

    "@pm2/io/semver/lru-cache": ["lru-cache@6.0.0", "", { "dependencies": { "yallist": "^4.0.0" } }, ""],

    "log-symbols/chalk/supports-color": ["supports-color@7.2.0", "", { "dependencies": { "has-flag": "^4.0.0" } }, ""],
  }
}
