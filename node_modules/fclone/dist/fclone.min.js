!function(e,t){"function"==typeof define&&define.amd?define("fclone",[],t):"object"==typeof module&&module.exports?module.exports=t():e.fclone=t()}(this,function(){"use strict";function e(e){if(Array.isArray(e))return!0;var t=e&&e.length;return"number"==typeof t&&(0===t||t-1 in e)&&"function"==typeof e.indexOf}function t(r,f){if(!r||"object"!==("undefined"==typeof r?"undefined":n(r)))return r;if(r instanceof Date)return new Date(r);if("undefined"!=typeof Buffer&&Buffer.isBuffer(r))return new Buffer(r);if("function"==typeof r.subarray&&/[A-Z][A-Za-z\d]+Array/.test(Object.prototype.toString.call(r)))return r.subarray(0);if(f||(f=[]),e(r)){f[f.length]=r;for(var o=r.length,u=-1,i=[];o>++u;)i[u]=~f.indexOf(r[u])?"[Circular]":t(r[u],f);return f.length&&f.length--,i}f[f.length]=r;var a={};r instanceof Error&&(a.name=r.name,a.message=r.message,a.stack=r.stack);for(var c=Object.keys(r),l=c.length;l--;){var y=c[l];a[y]=~f.indexOf(r[y])?"[Circular]":t(r[y],f)}return f.length&&f.length--,a}var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e};return t["default"]=t,t});