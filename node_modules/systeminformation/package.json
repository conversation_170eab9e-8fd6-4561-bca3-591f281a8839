{"name": "systeminformation", "version": "5.27.7", "description": "Advanced, lightweight system and OS information library", "license": "MIT", "author": "<PERSON> <<EMAIL>> (https://plus-innovations.com)", "homepage": "https://systeminformation.io", "main": "./lib/index.js", "type": "commonjs", "bin": {"systeminformation": "lib/cli.js"}, "types": "./lib/index.d.ts", "scripts": {"test": "node ./test/test.js", "testDeno": "deno run -A ./test/test.js"}, "files": ["lib/"], "keywords": ["system information", "sysinfo", "monitor", "monitoring", "os", "linux", "osx", "windows", "freebsd", "openbsd", "netbsd", "cpu", "cpuload", "physical cores", "logical cores", "processor", "cores", "threads", "socket type", "memory", "file system", "fsstats", "diskio", "block devices", "netstats", "network", "network interfaces", "network connections", "network stats", "iface", "printer", "processes", "users", "internet", "battery", "docker", "docker stats", "docker processes", "graphics", "graphic card", "graphic controller", "gpu", "display", "smart", "disk layout", "usb", "audio", "bluetooth", "wifi", "wifinetworks", "virtual box", "virtualbox", "vm", "backend", "hardware", "BIOS", "chassis"], "repository": {"type": "git", "url": "https://github.com/sebhildebrandt/systeminformation.git"}, "funding": {"type": "Buy me a coffee", "url": "https://www.buymeacoffee.com/systeminfo"}, "os": ["darwin", "linux", "win32", "freebsd", "openbsd", "netbsd", "sunos", "android"], "engines": {"node": ">=8.0.0"}}