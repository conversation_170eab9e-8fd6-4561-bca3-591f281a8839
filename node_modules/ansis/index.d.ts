type N=number;type S=string;type C='red'|'green'|'yellow'|'blue'|'magenta'|'cyan'|'white';type H=`${C}Bright`;export type AnsiColors='black'|C|'gray'|H|`bg${'Black'|Capitalize<C>|'Gray'|Capitalize<H>}`;export type AnsiStyles='reset'|'inverse'|'hidden'|'visible'|'bold'|'dim'|'italic'|'underline'|'strikethrough';type Q=(n:N)=>A;type L=(s:S)=>A;type R=(r:N,g:N,b:N)=>A;type A=Ansis;type Ansis={open:S;close:S;(v:unknown):S;(s:TemplateStringsArray,...v:any[]):S;fg:Q;bg:Q;rgb:R;bgRgb:R;hex:L;bgHex:L;isSupported():boolean;strip(s:S):S;extend<U extends S>(c:Record<U,S|{open:S;close:S}>):A&Record<U,A>}&{[K in AnsiStyles|AnsiColors]:A};declare const Ansis:new (n?:N)=>A,a:A,fg:Q,rgb:R,hex:L;export{a as default,Ansis,fg,fg as bg,rgb,rgb as bgRgb,hex,hex as bgHex,a as reset,a as inverse,a as hidden,a as visible,a as bold,a as dim,a as italic,a as underline,a as strikethrough,a as black,a as red,a as green,a as yellow,a as blue,a as magenta,a as cyan,a as white,a as gray,a as redBright,a as greenBright,a as yellowBright,a as blueBright,a as magentaBright,a as cyanBright,a as whiteBright,a as bgBlack,a as bgRed,a as bgGreen,a as bgYellow,a as bgBlue,a as bgMagenta,a as bgCyan,a as bgWhite,a as bgGray,a as bgRedBright,a as bgGreenBright,a as bgYellowBright,a as bgBlueBright,a as bgMagentaBright,a as bgCyanBright,a as bgWhiteBright};