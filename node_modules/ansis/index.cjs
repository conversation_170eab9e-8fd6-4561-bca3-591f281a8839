let e,l,{defineProperty:t,setPrototypeOf:r,create:n,keys:o}=Object,i="",{round:s,max:u}=Math,a=e=>{var l;let t=null===(l=/([a-f\d]{3,6})/i.exec(e))||void 0===l?void 0:l[1],r=null==t?void 0:t.length,n=parseInt(6^r?3^r?"0":t[0]+t[0]+t[1]+t[1]+t[2]+t[2]:t,16);return[n>>16&255,n>>8&255,255&n]},d=(e,l,t)=>e^l||l^t?16+36*s(e/51)+6*s(l/51)+s(t/51):8>e?16:e>248?231:s(24*(e-8)/247)+232,c=e=>{let l,t,r,n,o;return 8>e?30+e:16>e?e-8+90:(232>e?(o=(e-=16)%36,l=(e/36|0)/5,t=(o/6|0)/5,r=o%6/5):l=t=r=(10*(e-232)+8)/255,n=2*u(l,t,r),n?30+(s(r)<<2|s(t)<<1|s(l))+(2^n?0:60):30)};"undefined"==typeof globalThis&&(global.globalThis=global);let p,g=(()=>{var t,r,n,i;let s=e=>d.some((l=>e.test(l))),u=globalThis,a=null!==(t=u.process)&&void 0!==t?t:{},d=null!==(r=a.argv)&&void 0!==r?r:[],c=null!==(n=a.env)&&void 0!==n?n:{},p=-1;try{e=","+o(c).join(",")}catch(e){c={},p=0}let g="FORCE_COLOR",f=null!==(i={false:0,0:0,1:1,2:2,3:3}[c[g]])&&void 0!==i?i:-1,v=g in c&&f||s(/^--color=?(true|always)?$/);var b,h,O;return v&&(p=f),~p||(p=((t,r,n)=>(l=t.TERM,{"24bit":3,truecolor:3,ansi256:2,ansi:1}[t.COLORTERM]||(t.CI?/,GITHUB/.test(e)?3:1:r&&"dumb"!==l?n?3:/-256/.test(l)?2:1:0)))(c,!!c.PM2_HOME||(null===(h=c.NEXT_RUNTIME)||void 0===h?void 0:h.includes("edge"))||!(null===(b=a.stdout)||void 0===b||!b.isTTY),"win32"===a.platform)),!f||c.NO_COLOR||s(/^--(no-color|color=(false|never))$/)?0:null!==(O=u.window)&&void 0!==O&&O.chrome||v&&!p?3:p})(),f={open:i,close:i},v=39,b=49,h={},O=({p:e},{open:l,close:t})=>{let n=(e,...r)=>{if(!e){if(l&&l===t)return l;if((null!=e?e:i)===i)return i}let o,s=e.raw?String.raw({raw:e},...r):i+e,u=n.p,a=u.o,d=u.c;if(s.includes(""))for(;u;u=u.p){let{open:e,close:l}=u,t=l.length,r=i,n=0;if(t)for(;~(o=s.indexOf(l,n));n=o+t)r+=s.slice(n,o)+e;s=r+s.slice(n)}return a+(s.includes("\n")?s.replace(/(\r?\n)/g,d+"$1"+a):s)+d},o=l,s=t;return e&&(o=e.o+l,s=t+e.c),r(n,p),n.p={open:l,close:t,o,c:s,p:e},n.open=o,n.close=s,n};const m=function(e=g){let l={Ansis:m,isSupported:()=>o,strip:e=>e.replace(/[][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-ORZcf-nqry=><]/g,i),extend(e){for(let l in e){let r=e[l],n=(typeof r)[0],o="s"===n?R(...a(r)):r;h[l]="f"===n?{get(){return(...e)=>O(this,r(...e))}}:{get(){let e=O(this,o);return t(this,l,{value:e}),e}}}return p=n({},h),r(l,p),l}},o=e>0,s=(e,l)=>o?{open:`[${e}m`,close:`[${l}m`}:f,u=e=>l=>e(...a(l)),y=(e,l)=>(t,r,n)=>s(`${e}8;2;${t};${r};${n}`,l),w=(e,l)=>(t,r,n)=>s(((e,l,t)=>c(d(e,l,t)))(t,r,n)+e,l),T=e=>(l,t,r)=>e(d(l,t,r)),R=y(3,v),$=y(4,b),x=e=>s("38;5;"+e,v),C=e=>s("48;5;"+e,b);2===e?(R=T(x),$=T(C)):1===e&&(R=w(0,v),$=w(10,b),x=e=>s(c(e),v),C=e=>s(c(e)+10,b));let E,M={fg:x,bg:C,rgb:R,bgRgb:$,hex:u(R),bgHex:u($),visible:f,reset:s(0,0),bold:s(1,22),dim:s(2,22),italic:s(3,23),underline:s(4,24),inverse:s(7,27),hidden:s(8,28),strikethrough:s(9,29)},I="Bright";return"black,red,green,yellow,blue,magenta,cyan,white,gray".split(",").map(((e,l)=>{E="bg"+e[0].toUpperCase()+e.slice(1),8>l?(M[e+I]=s(90+l,v),M[E+I]=s(100+l,b)):l=60,M[e]=s(30+l,v),M[E]=s(40+l,b)})),l.extend(M)},y=new m;module.exports=y,y.default=y;
