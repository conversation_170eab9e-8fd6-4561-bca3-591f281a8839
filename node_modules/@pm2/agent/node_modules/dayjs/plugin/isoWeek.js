!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.dayjs_plugin_isoWeek=e()}(this,function(){"use strict";var t="day";return function(e,i,s){var a=function(e){return e.add(4-e.isoWeekday(),t)},d=i.prototype;d.isoWeekYear=function(){return a(this).year()},d.isoWeek=function(e){if(!this.$utils().u(e))return this.add(7*(e-this.isoWeek()),t);var i,d,n,r,o=a(this),u=(i=this.isoWeekYear(),d=this.$u,n=(d?s.utc:s)().year(i).startOf("year"),r=4-n.isoWeekday(),n.isoWeekday()>4&&(r+=7),n.add(r,t));return o.diff(u,"week")+1},d.isoWeekday=function(t){return this.$utils().u(t)?this.day()||7:this.day(this.day()%7?t:t-7)};var n=d.startOf;d.startOf=function(t,e){var i=this.$utils(),s=!!i.u(e)||e;return"isoweek"===i.p(t)?s?this.date(this.date()-(this.isoWeekday()-1)).startOf("day"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf("day"):n.bind(this)(t,e)}}});
