{"actions": [{"route": {"name": "/api/bucket/:id/actions/trigger", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "server_name", "type": "string", "description": "the name of the server", "optional": false, "defaultvalue": null}, {"name": "process_id", "type": "number", "description": "the id of the process", "optional": true, "defaultvalue": null}, {"name": "app_name", "type": "number", "description": "the name of the process", "optional": true, "defaultvalue": null}, {"name": "action_name", "type": "string", "description": "the name of the action to trigger", "optional": false, "defaultvalue": null}, {"name": "opts", "type": "object", "description": "any specific options to be passed to the function", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully run the action", "optional": false}], "response": [{"name": "success", "type": "boolean", "description": "succesully sended the action to PM2", "optional": false, "defaultvalue": null}], "name": "triggerAction", "longname": "Actions.triggerAction", "scope": "route"}, {"route": {"name": "/api/bucket/:id/actions/triggerPM2", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "server_name", "type": "string", "description": "the name of the server", "optional": false, "defaultvalue": null}, {"name": "method_name", "type": "string", "description": "the name of the pm2 method to trigger", "optional": false, "defaultvalue": null}, {"name": "app_name", "type": "string", "description": "the name of the application", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "400", "description": "failed action", "optional": false}, {"type": "200", "description": "succesfully run the action", "optional": false}], "response": [{"name": "success", "type": "boolean", "description": "succesully sended the action to PM2", "optional": false, "defaultvalue": null}], "name": "triggerPM2Action", "longname": "Actions.triggerPM2Action", "scope": "route"}, {"route": {"name": "/api/bucket/:id/actions/triggerScopedAction", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "server_name", "type": "string", "description": "the name of the server", "optional": false, "defaultvalue": null}, {"name": "action_name", "type": "string", "description": "the name of the pm2 method to trigger", "optional": false, "defaultvalue": null}, {"name": "app_name", "type": "string", "description": "the name of the application", "optional": false, "defaultvalue": null}, {"name": "pm_id", "type": "number", "description": "the id of the process", "optional": false, "defaultvalue": null}, {"name": "opts", "type": "object", "description": "custom parameters to give to the action", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully run the action", "optional": false}], "response": [{"name": ".", "type": "object", "description": "the action sended to the process", "optional": false, "defaultvalue": null}], "name": "triggerScopedAction", "longname": "Actions.triggerScopedAction", "scope": "route"}], "bucket": {"alert": {"analyzer": [{"route": {"name": "/api/bucket/:id/alerts/analyzer", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "size", "type": "integer", "description": "line limit, default to 20", "optional": true, "defaultvalue": null}, {"name": "from", "type": "integer", "description": "offset limit", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "list all alerts", "optional": false}], "name": "list", "longname": "Bucket.alert.analyzer.list", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/alerts/analyzer/:alert", "type": "PUT"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":alert", "type": "string", "description": "alert id", "optional": false}], "body": [{"name": "useful", "type": "boolean", "description": "", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "content modified", "optional": false}], "name": "editState", "longname": "Bucket.alert.analyzer.editState", "scope": "route"}, {"route": {"name": "/api/bucket/:id/alerts/analyzer/:analyzer/config", "type": "PUT"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":analyzer", "type": "string", "description": "analyzer name", "optional": false}], "body": [{"name": "blacklist", "type": "object", "description": "", "optional": false, "defaultvalue": null}, {"name": "blacklist.apps", "type": "array", "description": "", "optional": true, "defaultvalue": null}, {"name": "blacklist.servers", "type": "array", "description": "", "optional": true, "defaultvalue": null}, {"name": "blacklist.metrics", "type": "array", "description": "", "optional": true, "defaultvalue": null}, {"name": "threshold", "type": "number", "description": "", "optional": false, "defaultvalue": null}, {"name": "window", "type": "number", "description": "", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "content modified", "optional": false}], "name": "updateConfig", "longname": "Bucket.alert.analyzer.updateConfig", "scope": "route"}], "default": [{"route": {"name": "/api/bucket/:id/alerts", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "name", "type": "string", "description": "Alert name", "optional": false, "defaultvalue": null}, {"name": "enabled", "type": "boolean", "description": "<PERSON><PERSON>'s state", "optional": true, "defaultvalue": null}, {"name": "type", "type": "string", "description": "Should be `metric`, `event` or `webcheck`", "optional": false, "defaultvalue": null}, {"name": "initiator", "type": "string", "description": "Should be metric name or event name", "optional": false, "defaultvalue": null}, {"name": "options", "type": "object", "description": "", "optional": false, "defaultvalue": null}, {"name": "options.operator", "type": "string", "description": "Should be `>`, `<`, `=`, `>=` or `<=`", "optional": true, "defaultvalue": null}, {"name": "options.threshold", "type": "number", "description": "Value to reach to send an alert", "optional": true, "defaultvalue": null}, {"name": "options.act", "type": "string", "description": "Should be `always`, `opposite`, `first` or `diff`", "optional": true, "defaultvalue": null}, {"name": "options.timerange", "type": "number", "description": "Timerange to check, in seconds", "optional": true, "defaultvalue": null}, {"name": "scope", "type": "object", "description": "", "optional": false, "defaultvalue": null}, {"name": "scope.apps", "type": "object", "description": "Array of strings with apps name (can be empty)", "optional": true, "defaultvalue": null}, {"name": "scope.servers", "type": "object", "description": "Array of strings with servers name (can be empty)", "optional": true, "defaultvalue": null}, {"name": "scope.initiators", "type": "object", "description": "Array of strings with initiators name (need to be set if no apps or servers)", "optional": true, "defaultvalue": null}, {"name": "scope.sources", "type": "object", "description": "Array of strings with sources name (can be empty)", "optional": true, "defaultvalue": null}, {"name": "actions", "type": "object", "description": "List of actions to trigger", "optional": false, "defaultvalue": null}, {"name": "actions[].type", "type": "string", "description": "Type of action", "optional": true, "defaultvalue": null}, {"name": "actions[].params", "type": "object", "description": "Params for action", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "400", "description": "missing parameters", "optional": false}, {"type": "200", "description": "<PERSON>y created alert", "optional": false}], "name": "create", "longname": "Bucket.alert.create", "scope": "route"}, {"route": {"name": "/api/bucket/:id/alerts/:alert", "type": "DELETE"}, "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":alert", "type": "string", "description": "alert id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "204", "description": "successfuly deleted alert", "optional": false}], "name": "delete", "longname": "Bucket.alert.delete", "scope": "route", "authentication": false}, {"route": {"name": "/api/bucket/:id/alerts/", "type": "GET"}, "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "list all alerts", "optional": false}], "name": "list", "longname": "Bucket.alert.list", "scope": "route", "authentication": false}, {"route": {"name": "/api/bucket/:id/alerts/:alert", "type": "PUT"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":alert", "type": "string", "description": "alert id", "optional": false}], "body": [{"name": "name", "type": "string", "description": "Alert name", "optional": true, "defaultvalue": null}, {"name": "enabled", "type": "boolean", "description": "<PERSON><PERSON>'s state", "optional": true, "defaultvalue": null}, {"name": "type", "type": "string", "description": "Should be `metric`, `event` or `webcheck`", "optional": true, "defaultvalue": null}, {"name": "initiator", "type": "string", "description": "Should be metric name or event name", "optional": true, "defaultvalue": null}, {"name": "options", "type": "object", "description": "", "optional": true, "defaultvalue": null}, {"name": "options.operator", "type": "string", "description": "Should be `>`, `<`, `=`, `<=` or `>=`", "optional": true, "defaultvalue": null}, {"name": "options.threshold", "type": "number", "description": "Value to reach to send an alert", "optional": true, "defaultvalue": null}, {"name": "options.act", "type": "string", "description": "Should be `always`, `opposite`, `first` or `diff`", "optional": true, "defaultvalue": null}, {"name": "options.timerange", "type": "number", "description": "Timerange to check, in seconds", "optional": true, "defaultvalue": null}, {"name": "scope", "type": "object", "description": "", "optional": true, "defaultvalue": null}, {"name": "scope.apps", "type": "array", "description": "Array of strings with apps name (can be empty)", "optional": true, "defaultvalue": null}, {"name": "scope.servers", "type": "array", "description": "Array of strings with servers name (can be empty)", "optional": true, "defaultvalue": null}, {"name": "scope.initiators", "type": "object", "description": "Array of strings with initiators name (need to be set if no apps or servers)", "optional": true, "defaultvalue": null}, {"name": "scope.sources", "type": "object", "description": "Array of strings with sources name (can be empty)", "optional": true, "defaultvalue": null}, {"name": "actions", "type": "array", "description": "List of actions to trigger", "optional": true, "defaultvalue": null}, {"name": "actions[].type", "type": "string", "description": "Type of action", "optional": true, "defaultvalue": null}, {"name": "actions[].params", "type": "object", "description": "Params for action", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "400", "description": "missing parameters", "optional": false}, {"type": "404", "description": "alert not found", "optional": false}, {"type": "200", "description": "<PERSON>y created alert", "optional": false}], "name": "updateAlert", "longname": "Bucket.alert.updateAlert", "scope": "route"}, {"route": {"name": "/api/bucket/:id/alerts/:alert", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":alert", "type": "string", "description": "alert id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "404", "description": "alert not found", "optional": false}, {"type": "200", "description": "<PERSON><PERSON> returned alert", "optional": false}], "name": "get", "longname": "Bucket.alert.get", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/alerts/:alert/sample", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":alert", "type": "string", "description": "alert id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "404", "description": "alert not found", "optional": false}, {"type": "202", "description": "successfuly sended alert actions", "optional": false}], "name": "triggerSample", "longname": "Bucket.alert.triggerSample", "scope": "route"}, {"route": {"name": "/api/bucket/:id/alerts/update", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "triggers", "type": "object", "description": "", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "400", "description": "missing triggers parameter", "optional": false}, {"type": "200", "description": "succesfully update triggers", "optional": false}], "response": [{"name": "triggers", "type": "object", "description": "new triggers object", "optional": false, "defaultvalue": null}], "name": "update", "longname": "Bucket.alert.update", "scope": "route"}, {"route": {"name": "/api/bucket/:id/alerts/updateSlack", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "slack", "type": "object", "description": "", "optional": false, "defaultvalue": null}, {"name": "slack.active", "type": "boolean", "description": "", "optional": true, "defaultvalue": null}, {"name": "slack.url", "type": "boolean", "description": "needed if active is set to true", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "400", "description": "missing triggers parameter", "optional": false}, {"type": "200", "description": "succesfully update triggers", "optional": false}], "response": [{"name": "bucket", "type": "object", "description": "", "optional": false, "defaultvalue": null}], "name": "updateSlack", "longname": "Bucket.alert.updateSlack", "scope": "route"}, {"route": {"name": "/api/bucket/:id/alerts/updateWebhooks", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "webhooks", "type": "object", "description": "", "optional": false, "defaultvalue": null}, {"name": "webhooks.active", "type": "boolean", "description": "", "optional": true, "defaultvalue": null}, {"name": "webhooks.url", "type": "boolean", "description": "needed if active is set to true", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "400", "description": "missing triggers parameter", "optional": false}, {"type": "200", "description": "succesfully update triggers", "optional": false}], "response": [{"name": "bucket", "type": "object", "description": "", "optional": false, "defaultvalue": null}], "name": "updateWebhooks", "longname": "Bucket.alert.updateWebhooks", "scope": "route"}]}, "application": [{"route": {"name": "/api/bucket/:id/applications", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "successfuly retrieved applications", "optional": false}], "name": "list", "longname": "Bucket.application.list", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/applications/:application", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":application", "type": "string", "description": "application id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "404", "description": "application not found", "optional": false}, {"type": "200", "description": "successfuly retrieved application", "optional": false}], "name": "get", "longname": "Bucket.application.get", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/applications", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "name", "type": "string", "description": "", "optional": false, "defaultvalue": null}, {"name": "domains", "type": "object", "description": "Array of string with domains", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "400", "description": "missing parameters", "optional": false}, {"type": "200", "description": "successfuly created application", "optional": false}], "name": "create", "longname": "Bucket.application.create", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/applications/:application", "type": "PUT"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":application", "type": "string", "description": "application id", "optional": false}], "body": [{"name": "name", "type": "string", "description": "", "optional": true, "defaultvalue": null}, {"name": "domains", "type": "object", "description": "", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "400", "description": "missing parameters", "optional": false}, {"type": "200", "description": "successfuly updated application", "optional": false}], "name": "update", "longname": "Bucket.application.update", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/applications/:application", "type": "DELETE"}, "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":application", "type": "string", "description": "application id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "204", "description": "successfuly deleted application", "optional": false}], "name": "delete", "longname": "Bucket.application.delete", "scope": "route", "async": true, "authentication": false}, {"route": {"name": "/api/bucket/:id/applications/:application/preview", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":application", "type": "string", "description": "application id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "404", "description": "preview not found", "optional": false}, {"type": "200", "description": "successfuly retrieved application screenshot", "optional": false}], "name": "getPreview", "longname": "Bucket.application.getPreview", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/applications/:application/report", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":application", "type": "string", "description": "application id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "404", "description": "report not found", "optional": false}, {"type": "200", "description": "successfuly retrieved application report", "optional": false}], "name": "getReports", "longname": "Bucket.application.getReports", "scope": "route", "async": true}], "billing": [{"route": {"name": "/api/bucket/:id/payment/subscribe", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "plan", "type": "string", "description": "name of the plan to upgrade to", "optional": false, "defaultvalue": null}, {"name": "stripe_token", "type": "string", "description": "a card token created by stripe", "optional": true, "defaultvalue": null}, {"name": "coupon_id", "type": "string", "description": "the id of the stripe coupon", "optional": true, "defaultvalue": null}], "code": [{"type": "400", "description": "missing/invalid parameters", "optional": false}, {"type": "403", "description": "need a credit card OR not allowed to subscribe to the plan", "optional": false}, {"type": "500", "description": "stripe/database error", "optional": false}, {"type": "200", "description": "succesfully upgraded", "optional": false}], "response": [{"name": "bucket", "type": "object", "description": "the bucket object", "optional": false, "defaultvalue": null}, {"name": "subscription", "type": "object", "description": "the subscription object attached to the subscription", "optional": false, "defaultvalue": null}], "name": "subscribe", "longname": "Bucket.billing.subscribe", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/payment/subscribe/:paymentIntent", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":paymentIntent", "type": "string", "description": "paymentIntent id", "optional": false}], "body": [{"name": "plan", "type": "string", "description": "name of the plan to upgrade to", "optional": false, "defaultvalue": null}], "code": [{"type": "400", "description": "missing/invalid parameters", "optional": false}, {"type": "500", "description": "stripe/database error", "optional": false}, {"type": "200", "description": "succesfully upgraded", "optional": false}], "response": [{"name": "bucket", "type": "object", "description": "the bucket object", "optional": false, "defaultvalue": null}], "name": "paymentIntentSucceed", "longname": "Bucket.billing.paymentIntentSucceed", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/payment/trial", "type": "PUT"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "plan", "type": "string", "description": "Plan to trial", "optional": false, "defaultvalue": null}], "code": [{"type": "400", "description": "can't claim trial", "optional": false}, {"type": "200", "description": "trial launched", "optional": false}], "response": [{"name": "duration", "type": "string", "description": "the duration of the trial", "optional": false, "defaultvalue": null}, {"name": "plan", "type": "string", "description": "the plan of the trial", "optional": false, "defaultvalue": null}], "name": "startTrial", "longname": "Bucket.billing.startTrial", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/payment/invoices", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "400", "description": "Missing/invalid parameters", "optional": false}, {"type": "404", "description": "This bucket hasn't invoices", "optional": false}, {"type": "200", "description": "succesfully returns invoices", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of invoices", "optional": false, "defaultvalue": null}], "name": "getInvoices", "longname": "Bucket.billing.getInvoices", "scope": "route"}, {"route": {"name": "/api/bucket/:id/payment/receipts", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "400", "description": "Missing/invalid parameters", "optional": false}, {"type": "404", "description": "This bucket hasn't receipts", "optional": false}, {"type": "200", "description": "succesfully returns receipts", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of receipts", "optional": false, "defaultvalue": null}], "name": "getReceipts", "longname": "Bucket.billing.getReceipts", "scope": "route"}, {"route": {"name": "/api/bucket/:id/payment/subscription", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "404", "description": "the bucket doesnt have any subscription", "optional": false}, {"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved the subscription", "optional": false}], "response": [{"name": ".", "type": "object", "description": "subscription object", "optional": false, "defaultvalue": null}], "name": "getSubcription", "longname": "Bucket.billing.getSubcription", "scope": "route"}, {"route": {"name": "/api/bucket/:id/payment/subscription/state", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "404", "description": "the bucket doesnt have any subscription", "optional": false}, {"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved the subscription", "optional": false}], "response": [{"name": "status", "type": "string", "description": "stripe state of the subscription", "optional": false, "defaultvalue": null}, {"name": "plan", "type": "string", "description": "stripe plan name of the subscription", "optional": false, "defaultvalue": null}, {"name": "canceled_at", "type": "string", "description": "if he sub has been cancelled, add the date", "optional": false, "defaultvalue": null}], "name": "getSubcriptionState", "longname": "Bucket.billing.getSubcriptionState", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/payment/cards", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "token", "type": "string", "description": "card token generated by stripe", "optional": false, "defaultvalue": null}], "code": [{"type": "400", "description": "missing parameters", "optional": false}, {"type": "500", "description": "stripe error", "optional": false}, {"type": "200", "description": "succesfully added the card", "optional": false}], "response": [{"name": "data", "type": "object", "description": "stripe credit card Object", "optional": false, "defaultvalue": null}], "name": "attachCreditCard", "longname": "Bucket.billing.attachCreditCard", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/payment/cards", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "stripe error", "optional": false}, {"type": "404", "description": "the user doesn't have any default card", "optional": false}, {"type": "200", "description": "succesfully retieved the charges", "optional": false}], "response": [{"name": "data", "type": "array", "description": "list of stripe cards object", "optional": false, "defaultvalue": null}], "name": "fetchCreditCards", "longname": "Bucket.billing.fetchCreditCards", "scope": "route"}, {"route": {"name": "/api/bucket/:id/payment/card/:card_id", "type": "GET"}, "authentication": true, "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":card_id", "type": "string", "description": "the stripe id of the card", "optional": false}], "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "stripe error", "optional": false}, {"type": "400", "description": "missing parameters card_id", "optional": false}, {"type": "404", "description": "the user doesn't have any default card", "optional": false}, {"type": "200", "description": "succesfully retieved the card", "optional": false}], "response": [{"name": "data", "type": "array", "description": "stripe card object", "optional": false, "defaultvalue": null}], "name": "fetchCreditCard", "longname": "Bucket.billing.fetchCreditCard", "scope": "route"}, {"route": {"name": "/api/bucket/:id/payment/card", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "stripe error", "optional": false}, {"type": "404", "description": "the user doesn't have any default card", "optional": false}, {"type": "200", "description": "succesfully retieved the card", "optional": false}], "response": [{"name": "data", "type": "array", "description": "stripe card object", "optional": false, "defaultvalue": null}], "name": "fetchDefaultCreditCard", "longname": "Bucket.billing.fetchDefaultCreditCard", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/payment/card", "type": "PUT"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "id", "type": "string", "description": "stripe card id", "optional": false, "defaultvalue": null}, {"name": "address_line1", "type": "string", "description": "", "optional": true, "defaultvalue": null}, {"name": "address_country", "type": "string", "description": "", "optional": true, "defaultvalue": null}, {"name": "address_zip", "type": "string", "description": "", "optional": true, "defaultvalue": null}, {"name": "address_city", "type": "string", "description": "", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "stripe error", "optional": false}, {"type": "400", "description": "missing parameters, you need to specify a card", "optional": false}, {"type": "200", "description": "succesfully updated the card", "optional": false}], "response": [{"name": "data", "type": "array", "description": "stripe card object", "optional": false, "defaultvalue": null}], "name": "updateCreditCard", "longname": "Bucket.billing.updateCreditCard", "scope": "route"}, {"route": {"name": "/api/bucket/:id/payment/card/:card_id", "type": "DELETE"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":card_id", "type": "string", "description": "the stripe id of the card", "optional": false}], "code": [{"type": "500", "description": "stripe error", "optional": false}, {"type": "400", "description": "missing parameters card_id", "optional": false}, {"type": "200", "description": "succesfully retieved the card", "optional": false}, {"type": "403", "description": "the user must have one card active when having a subscription", "optional": false}], "response": [{"name": ".", "type": "object", "description": "stripe card object", "optional": false, "defaultvalue": null}], "name": "deleteCreditCard", "longname": "Bucket.billing.deleteCreditCard", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/payment/card/:card_id/default", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":card_id", "type": "string", "description": "the stripe id of the card", "optional": false}], "code": [{"type": "500", "description": "stripe error", "optional": false}, {"type": "400", "description": "missing parameters card_id", "optional": false}, {"type": "200", "description": "succesfully set the card as default", "optional": false}], "response": [{"name": "data", "type": "object", "description": "stripe card object", "optional": false, "defaultvalue": null}], "name": "setDefaultCard", "longname": "Bucket.billing.setDefaultCard", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/payment", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "stripe error", "optional": false}, {"type": "400", "description": "missing parameters card_id", "optional": false}, {"type": "200", "description": "succesfully retrieved the metadata", "optional": false}], "response": [{"name": ".", "type": "object", "description": "stripe metadata object", "optional": false, "defaultvalue": null}], "name": "fetchMetadata", "longname": "Bucket.billing.fetchMetadata", "scope": "route"}, {"route": {"name": "/api/bucket/:id/payment", "type": "PUT"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "metadata", "type": "object", "description": "the metadata you can update", "optional": false, "defaultvalue": null}, {"name": "metadata.vat_number", "type": "string", "description": "", "optional": true, "defaultvalue": null}, {"name": "metadata.company_name", "type": "string", "description": "", "optional": true, "defaultvalue": null}, {"name": "metadata.receipt_email", "type": "string", "description": "", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "stripe error", "optional": false}, {"type": "400", "description": "missing parameters, you need to specify a card", "optional": false}, {"type": "200", "description": "succesfully updated the card", "optional": false}], "response": [{"name": "data", "type": "array", "description": "stripe customer metadata object", "optional": false, "defaultvalue": null}], "name": "updateMetadata", "longname": "Bucket.billing.updateMetadata", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/payment/banks", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "iban", "type": "string", "description": "the iban used to recognize the account", "optional": true, "defaultvalue": null}, {"name": "type", "type": "string", "description": "the type of the bank account (currently only sepa is available)", "optional": false, "defaultvalue": null}, {"name": "name", "type": "string", "description": "name of the bank account owner", "optional": false, "defaultvalue": null}], "code": [{"type": "400", "description": "missing parameters", "optional": false}, {"type": "500", "description": "stripe error", "optional": false}, {"type": "200", "description": "succesfully added the account", "optional": false}], "response": [{"name": "data", "type": "object", "description": "stripe credit card Object", "optional": false, "defaultvalue": null}], "name": "attachBankAccount", "longname": "Bucket.billing.attachBankAccount", "scope": "route"}, {"route": {"name": "/api/bucket/:id/payment/banks", "type": "GET"}, "authentication": true, "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "stripe error", "optional": false}, {"type": "404", "description": "the user doesn't have any default account", "optional": false}, {"type": "200", "description": "succesfully retieved the card", "optional": false}], "response": [{"name": "data", "type": "object", "description": "stripe source object", "optional": false, "defaultvalue": null}], "name": "fetchBankAccount", "longname": "Bucket.billing.fetchBankAccount", "scope": "route"}, {"route": {"name": "/api/bucket/:id/payment/banks", "type": "DELETE"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "stripe error", "optional": false}, {"type": "200", "description": "succesfully retieved the card", "optional": false}, {"type": "404", "description": "the user doesn't have any default account", "optional": false}], "response": [{"name": ".", "type": "object", "description": "stripe source object", "optional": false, "defaultvalue": null}], "name": "deleteBankAccount", "longname": "Bucket.billing.deleteBankAccount", "scope": "route"}], "dashboardschema": [{"route": {"name": "/api/bucket/:id/dashboardSchema/", "type": "PUT"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "name", "type": "string", "description": "the name of the dashboard", "optional": false, "defaultvalue": null}, {"name": "data", "type": "object", "description": "the list of component that compose the dashboard", "optional": false, "defaultvalue": null}, {"name": "mode", "type": "string", "description": "the dashboard mode", "optional": false, "defaultvalue": null}, {"name": "image", "type": "object", "description": "background image for the dashboard", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully created dashboard", "optional": false}, {"type": "400", "description": "Invalid params", "optional": false}], "response": [{"name": ".", "type": "dashboard", "description": "complete dashboard object from database", "optional": false, "defaultvalue": null}], "name": "create", "longname": "Bucket.dashboardschema.create", "scope": "route"}, {"route": {"name": "/api/bucket/:id/dashboardSchema/", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}, {"type": "400", "description": "Invalid params", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of servers status", "optional": false, "defaultvalue": null}], "name": "retrieveAll", "longname": "Bucket.dashboardschema.retrieveAll", "scope": "route"}, {"route": {"name": "/api/bucket/:id/dashboardSchema/:dashid/visualization", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":dashid", "type": "string", "description": "dashboard id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}, {"type": "404", "description": "dashboard not found", "optional": false}, {"type": "400", "description": "Invalid params", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of dashboards", "optional": false, "defaultvalue": null}], "name": "visualization", "longname": "Bucket.dashboardschema.visualization", "scope": "route"}, {"route": {"name": "/api/bucket/:id/dashboardSchema/:dashid", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":dashid", "type": "string", "description": "dashboard id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}, {"type": "404", "description": "dashboard not found", "optional": false}, {"type": "400", "description": "Invalid params", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of dashboards", "optional": false, "defaultvalue": null}], "name": "retrieve", "longname": "Bucket.dashboardschema.retrieve", "scope": "route"}, {"route": {"name": "/api/bucket/:id/dashboardSchema/:dashid", "type": "DELETE"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":dashid", "type": "string", "description": "dashboard id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully deleted dashboard", "optional": false}, {"type": "400", "description": "Invalid params", "optional": false}, {"type": "404", "description": "dashboard not found", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of dashboards", "optional": false, "defaultvalue": null}], "name": "remove", "longname": "Bucket.dashboardschema.remove", "scope": "route"}, {"route": {"name": "/api/bucket/:id/dashboardSchema/:dashId", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":dashId", "type": "string", "description": "dashboard id", "optional": false}], "body": [{"name": "name", "type": "string", "description": "the name of the dashboard", "optional": false, "defaultvalue": null}, {"name": "data", "type": "object", "description": "the data to populate the dashboard", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}, {"type": "404", "description": "dashboard not found", "optional": false}, {"type": "400", "description": "Invalid params", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of servers status", "optional": false, "defaultvalue": null}], "name": "update", "longname": "Bucket.dashboardschema.update", "scope": "route"}], "server": [{"route": {"name": "/api/bucket/:id/data/deleteServer", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "server_name", "type": "string", "description": "the name of server", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "406", "description": "require an action before delete", "optional": false}, {"type": "400", "description": "missing or invalid parameters", "optional": false}, {"type": "200", "description": "successfully deleted", "optional": false}], "response": [{"name": "success", "type": "boolean", "description": "can be true or false", "optional": false, "defaultvalue": null}, {"name": "msg", "type": "string", "description": "response", "optional": false, "defaultvalue": null}], "name": "deleteServer", "longname": "Bucket.server.deleteServer", "scope": "route"}], "webcheck": [{"route": {"name": "/api/bucket/:id/webchecks/metrics", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "successfuly retrieved webchecks metrics", "optional": false}], "name": "listMetrics", "longname": "Bucket.webcheck.listMetrics", "scope": "route"}, {"route": {"name": "/api/bucket/:id/webchecks/regions", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "successfuly retrieved webchecks regions", "optional": false}], "name": "listRegions", "longname": "Bucket.webcheck.listRegions", "scope": "route"}, {"route": {"name": "/api/bucket/:id/webchecks/:webcheck/metrics", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":webcheck", "type": "string", "description": "webcheck id", "optional": false}], "body": [{"name": "start", "type": "string", "description": "", "optional": true, "defaultvalue": null}, {"name": "metrics", "type": "array", "description": "", "optional": true, "defaultvalue": null}, {"name": "end", "type": "string", "description": "", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "successfuly retrieved webchecks regions", "optional": false}], "name": "getMetrics", "longname": "Bucket.webcheck.getMetrics", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/webchecks", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "query": [{"name": "application", "type": "string", "description": "Application's id to filter", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "<PERSON>y retrieved webchecks", "optional": false}], "name": "list", "longname": "Bucket.webcheck.list", "scope": "route"}, {"route": {"name": "/api/bucket/:id/webchecks/:webcheck", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":webcheck", "type": "string", "description": "webcheck id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "404", "description": "webcheck not found", "optional": false}, {"type": "200", "description": "<PERSON>y retrieved webcheck", "optional": false}], "name": "get", "longname": "Bucket.webcheck.get", "scope": "route"}, {"route": {"name": "/api/bucket/:id/webchecks", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "name", "type": "string", "description": "Webcheck name", "optional": false, "defaultvalue": null}, {"name": "enabled", "type": "boolean", "description": "Webcheck's state", "optional": true, "defaultvalue": null}, {"name": "target", "type": "object", "description": "", "optional": false, "defaultvalue": null}, {"name": "target.type", "type": "string", "description": "Should be `http`, `https` or `tcp`", "optional": true, "defaultvalue": null}, {"name": "target.port", "type": "number", "description": "Target's port", "optional": true, "defaultvalue": null}, {"name": "target.address", "type": "string", "description": "Target's IP/domain", "optional": true, "defaultvalue": null}, {"name": "target.path", "type": "string", "description": "HTTP Path (only for http/https)", "optional": true, "defaultvalue": null}, {"name": "target.is_frontend", "type": "boolean", "description": "Enable or disable frontend metrics (via puppeteer)", "optional": true, "defaultvalue": null}, {"name": "body", "type": "string", "description": "Body need to match this regex to succeed webcheck (only for http/https)", "optional": true, "defaultvalue": null}, {"name": "interval", "type": "number", "description": "Webcheck's interval check (ms)", "optional": false, "defaultvalue": null}, {"name": "timeout", "type": "number", "description": "Webcheck's timeout (ms)", "optional": false, "defaultvalue": null}, {"name": "source", "type": "object", "description": "", "optional": false, "defaultvalue": null}, {"name": "source.region", "type": "string", "description": "Webcheck's worker region", "optional": true, "defaultvalue": null}, {"name": "retry", "type": "object", "description": "", "optional": false, "defaultvalue": null}, {"name": "retry.max", "type": "number", "description": "<PERSON> webcheck's retry before mark as failed", "optional": true, "defaultvalue": null}, {"name": "retry.interval", "type": "number", "description": "Webcheck's retry interval (ms)", "optional": true, "defaultvalue": null}, {"name": "alerts", "type": "object", "description": "List of alerts (cf. Alert type)", "optional": false, "defaultvalue": null}, {"name": "application", "type": "string", "description": "Application's id", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "400", "description": "missing parameters", "optional": false}, {"type": "200", "description": "<PERSON><PERSON> created webcheck", "optional": false}], "name": "create", "longname": "Bucket.webcheck.create", "scope": "route"}, {"route": {"name": "/api/bucket/:id/webchecks/:webcheck", "type": "PUT"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":webcheck", "type": "string", "description": "webcheck id", "optional": false}], "body": [{"name": "name", "type": "string", "description": "Webcheck name", "optional": true, "defaultvalue": null}, {"name": "enabled", "type": "boolean", "description": "Webcheck's state", "optional": true, "defaultvalue": null}, {"name": "target", "type": "object", "description": "", "optional": true, "defaultvalue": null}, {"name": "target.type", "type": "string", "description": "Should be `http`, `https` or `tcp`", "optional": true, "defaultvalue": null}, {"name": "target.port", "type": "number", "description": "Target's port", "optional": true, "defaultvalue": null}, {"name": "target.address", "type": "string", "description": "Target's IP/domain", "optional": true, "defaultvalue": null}, {"name": "target.path", "type": "string", "description": "HTTP Path (only for http/https)", "optional": true, "defaultvalue": null}, {"name": "target.is_frontend", "type": "boolean", "description": "Enable or disable frontend metrics (via puppeteer)", "optional": true, "defaultvalue": null}, {"name": "body", "type": "string", "description": "Body need to match this regex to succeed webcheck (only for http/https)", "optional": true, "defaultvalue": null}, {"name": "interval", "type": "number", "description": "Webcheck's interval check (ms)", "optional": true, "defaultvalue": null}, {"name": "timeout", "type": "number", "description": "Webcheck's timeout (ms)", "optional": true, "defaultvalue": null}, {"name": "source", "type": "object", "description": "", "optional": true, "defaultvalue": null}, {"name": "source.region", "type": "string", "description": "Webcheck's worker region", "optional": true, "defaultvalue": null}, {"name": "retry", "type": "object", "description": "", "optional": true, "defaultvalue": null}, {"name": "retry.max", "type": "number", "description": "<PERSON> webcheck's retry before mark as failed", "optional": true, "defaultvalue": null}, {"name": "retry.interval", "type": "number", "description": "Webcheck's retry interval (ms)", "optional": true, "defaultvalue": null}, {"name": "alerts", "type": "object", "description": "List of alerts (cf. Alert type)", "optional": true, "defaultvalue": null}, {"name": "application", "type": "string", "description": "Application's id", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "400", "description": "missing parameters", "optional": false}, {"type": "200", "description": "successfuly updated webcheck", "optional": false}], "name": "update", "longname": "Bucket.webcheck.update", "scope": "route"}, {"route": {"name": "/api/bucket/:id/webchecks/:webcheck", "type": "DELETE"}, "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":webcheck", "type": "string", "description": "webcheck id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "204", "description": "<PERSON>y deleted webcheck", "optional": false}], "name": "delete", "longname": "Bucket.webcheck.delete", "scope": "route", "authentication": false}], "default": [{"route": {"name": "/api/bucket/:id/feedback", "type": "PUT"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "feedback", "type": "string", "description": "the feedback text", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "400", "description": "missing feedback field", "optional": false}, {"type": "200", "description": "succesfully registered the feedback", "optional": false}], "response": [{"name": "feedback", "type": "string", "description": "the feedback that hasn't been registered", "optional": false, "defaultvalue": null}], "name": "sendFeedback", "longname": "Bucket.sendFeedback", "scope": "route"}, {"name": "retrieveUsers", "route": {"name": "/api/bucket/:id/users_authorized", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved bucket's members", "optional": false}], "response": [{"name": ".", "type": "array", "description": "a array of user containing their email, username and roles", "optional": false, "defaultvalue": null}], "longname": "Bucket.retrieveUsers", "scope": "route"}, {"name": "currentRole", "route": {"name": "/api/bucket/:id/current_role", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "200", "description": "succesfully retrieved the use role", "optional": false}], "response": [{"name": "role", "type": "string", "description": "the user role", "optional": false, "defaultvalue": null}], "longname": "Bucket.currentRole", "scope": "route"}, {"route": {"name": "/api/bucket/:id/manage_notif", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "email", "type": "string", "description": "the user email", "optional": false, "defaultvalue": null}, {"name": "state", "type": "string", "description": "the notification state you want to set for that user\n (either 'email' or 'nonde)", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "404", "description": "user not found", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of state for each user", "optional": false, "defaultvalue": null}], "name": "setNotificationState", "longname": "Bucket.setNotificationState", "scope": "route"}, {"name": "inviteUser", "route": {"name": "/api/bucket/:id/add_user", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "email", "type": "string", "description": "the email of the user", "optional": false, "defaultvalue": null}], "code": [{"type": "400", "description": "missing/invalid parameters", "optional": false}, {"type": "403", "description": "you cant invit more users because you hit the bucket limit", "optional": false}, {"type": "200", "description": "succesfully invited the user (either directly or by email)", "optional": false}], "response": [{"name": "invitations", "type": "array", "description": "the list of invitations actually active", "optional": false, "defaultvalue": null}], "longname": "Bucket.inviteUser", "scope": "route"}, {"route": {"name": "/api/bucket/:id/invitation", "type": "DELETE"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "query": [{"name": "email", "type": "string", "description": "the email of the invitation you want to delete", "optional": true, "defaultvalue": null}], "code": [{"type": "400", "description": "invalid/missing parameters", "optional": false}, {"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully deleted the invitation", "optional": false}], "response": [{"name": "invitations", "type": "array", "description": "the list of invitations actually active", "optional": false, "defaultvalue": null}], "name": "removeInvitation", "longname": "Bucket.removeInvitation", "scope": "route"}, {"route": {"name": "/api/bucket/:id/remove_user", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "email", "type": "string", "description": "the email of the user you want to remove", "optional": false, "defaultvalue": null}], "code": [{"type": "400", "description": "missing/invalid parameters", "optional": false}, {"type": "404", "description": "user not found", "optional": false}, {"type": "403", "description": "impossible to remove the owner from the bucket", "optional": false}, {"type": "500", "description": "database error", "optional": false}], "response": [{"name": ".", "type": "array", "description": "a array of user containing their email, username and roles", "optional": false, "defaultvalue": null}], "name": "removeUser", "longname": "Bucket.removeUser", "scope": "route"}, {"route": {"name": "/api/bucket/:id/promote_user", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "email", "type": "string", "description": "the email of the user you want to change the role", "optional": false, "defaultvalue": null}, {"name": "role", "type": "string", "description": "the role you want to set", "optional": false, "defaultvalue": null}], "code": [{"type": "400", "description": "invalid/missing parameters", "optional": false}, {"type": "404", "description": "user not found", "optional": false}, {"type": "403", "description": "impossible to set the role of the owner", "optional": false}], "response": [{"name": ".", "type": "array", "description": "a array of user containing their email, username and roles", "optional": false, "defaultvalue": null}], "name": "setUserRole", "longname": "Bucket.setUserRole", "scope": "route"}, {"name": "retrieveAll", "route": {"name": "/api/bucket/", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully fetched bucket", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of buckets", "optional": false, "defaultvalue": null}], "longname": "Bucket.retrieveAll", "scope": "route"}, {"name": "create", "route": {"name": "/api/bucket/create_classic", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "body": [{"name": "name", "type": "string", "description": "the name of the bucket", "optional": false, "defaultvalue": null}, {"name": "comment", "type": "string", "description": "any comments that will be written under the bucket name", "optional": true, "defaultvalue": null}, {"name": "app_url", "type": "string", "description": "", "optional": true, "defaultvalue": null}], "code": [{"type": "400", "description": "missing parameters", "optional": false}, {"type": "403", "description": "you cant create any more bucket", "optional": false}, {"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully created a bucket", "optional": false}], "response": [{"name": "bucket", "type": "object", "description": "the created bucket", "optional": false, "defaultvalue": null}], "longname": "Bucket.create", "scope": "route"}, {"deprecated": true, "route": {"name": "/api/bucket/:id/start_trial", "type": "PUT"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "400", "description": "can't claim trial", "optional": false}, {"type": "200", "description": "trial launched", "optional": false}], "response": [{"name": "duration", "type": "string", "description": "the duration of the trial", "optional": false, "defaultvalue": null}, {"name": "plan", "type": "string", "description": "the plan of the trial", "optional": false, "defaultvalue": null}], "name": "claimTrial", "longname": "Bucket.claimTrial", "scope": "route"}, {"deprecated": true, "name": "upgrade", "route": {"name": "/api/bucket/:id/upgrade", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "plan", "type": "string", "description": "name of the plan to upgrade to", "optional": false, "defaultvalue": null}, {"name": "stripe_token", "type": "string", "description": "a card token created by stripe", "optional": true, "defaultvalue": null}, {"name": "coupon_id", "type": "string", "description": "the id of the stripe coupon", "optional": true, "defaultvalue": null}], "code": [{"type": "400", "description": "missing/invalid parameters", "optional": false}, {"type": "403", "description": "need a credit card OR not allowed to subscribe to the plan", "optional": false}, {"type": "500", "description": "stripe/database error", "optional": false}, {"type": "200", "description": "succesfully upgraded", "optional": false}], "response": [{"name": "bucket", "type": "object", "description": "the bucket object", "optional": false, "defaultvalue": null}, {"name": "subscription", "type": "object", "description": "the subscription object attached to the subscription", "optional": false, "defaultvalue": null}], "longname": "Bucket.upgrade", "scope": "route"}, {"name": "retrieve", "route": {"name": "/api/bucket/:id", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "200", "description": "succesfully retrieved the bucket", "optional": false}], "response": [{"name": ".", "type": "object", "description": "bucket object", "optional": false, "defaultvalue": null}], "longname": "Bucket.retrieve", "scope": "route"}, {"route": {"name": "/api/bucket/:id", "type": "PUT"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "name", "type": "string", "description": "", "optional": true, "defaultvalue": null}, {"name": "comment", "type": "string", "description": "", "optional": true, "defaultvalue": null}, {"name": "app_url", "type": "string", "description": "", "optional": true, "defaultvalue": null}, {"name": "configuration", "type": "string", "description": "", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "400", "description": "missing parameters", "optional": false}], "response": [{"name": ".", "type": "object", "description": "bucket object", "optional": false, "defaultvalue": null}], "name": "update", "longname": "Bucket.update", "scope": "route", "async": true}, {"name": "retrieveServers", "route": {"name": "/api/bucket/:id/meta_servers", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved the server's metadata", "optional": false}], "response": [{"name": ".", "type": "array", "description": "servers metadata", "optional": false, "defaultvalue": null}], "longname": "Bucket.retrieveServers", "scope": "route"}, {"route": {"name": "/api/bucket/:id", "type": "DELETE"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully deleted the bucket", "optional": false}], "response": [{"name": ".", "type": "object", "description": "the deleted bucket", "optional": false, "defaultvalue": null}], "name": "destroy", "longname": "Bucket.destroy", "scope": "route"}, {"route": {"name": "/api/bucket/:id/transfer_ownership", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "new_owner", "type": "string", "description": "the wanted owner's email", "optional": false, "defaultvalue": null}], "code": [{"type": "400", "description": "Missing/invalid parameters", "optional": false}, {"type": "404", "description": "user not found", "optional": false}, {"type": "403", "description": "the new owner need to have a active credit card", "optional": false}, {"type": "200", "description": "succesfully transfered the bucket, old owner is now admin", "optional": false}], "response": [{"name": ".", "type": "object", "description": "bucket object", "optional": false, "defaultvalue": null}], "name": "transferOwnership", "longname": "Bucket.transferOwnership", "scope": "route"}, {"route": {"name": "/api/bucket/:id/user_options", "type": "PUT"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "options", "type": "object", "description": "user options", "optional": false, "defaultvalue": null}], "code": [{"type": "200", "description": "succesfully update user options", "optional": false}, {"type": "400", "description": "missing parameters", "optional": false}], "response": [{"name": "bucket", "type": "object", "description": "", "optional": false, "defaultvalue": null}], "name": "updateUserOptions", "longname": "Bucket.updateUserOptions", "scope": "route"}]}, "dashboard": [{"route": {"name": "/api/bucket/:id/dashboard/", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}, {"type": "400", "description": "Invalid params", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of servers status", "optional": false, "defaultvalue": null}], "name": "retrieveAll", "longname": "Dashboard.retrieveAll", "scope": "route"}, {"route": {"name": "/api/bucket/:id/dashboard/:dashid", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":dashid", "type": "string", "description": "dashboard id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}, {"type": "404", "description": "dashboard not found", "optional": false}, {"type": "400", "description": "Invalid params", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of dashboards", "optional": false, "defaultvalue": null}], "name": "retrieve", "longname": "Dashboard.retrieve", "scope": "route"}, {"route": {"name": "/api/bucket/:id/dashboard/:dashid", "type": "DELETE"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":dashid", "type": "string", "description": "dashboard id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully deleted dashboard", "optional": false}, {"type": "400", "description": "Invalid params", "optional": false}, {"type": "404", "description": "dashboard not found", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of dashboards", "optional": false, "defaultvalue": null}], "name": "remove", "longname": "Dashboard.remove", "scope": "route"}, {"route": {"name": "/api/bucket/:id/dashboard/:dashId", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":dashId", "type": "string", "description": "dashboard id", "optional": false}], "body": [{"name": "name", "type": "string", "description": "the name of the dashboard", "optional": false, "defaultvalue": null}, {"name": "children", "type": "object", "description": "the list of component that compose the dashboard", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}, {"type": "404", "description": "dashboard not found", "optional": false}, {"type": "400", "description": "Invalid params", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of servers status", "optional": false, "defaultvalue": null}], "name": "update", "longname": "Dashboard.update", "scope": "route"}, {"route": {"name": "/api/bucket/:id/dashboard/", "type": "PUT"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "name", "type": "string", "description": "the name of the dashboard", "optional": false, "defaultvalue": null}, {"name": "children", "type": "object", "description": "the list of component that compose the dashboard", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully created dashboard", "optional": false}, {"type": "400", "description": "Invalid params", "optional": false}], "response": [{"name": ".", "type": "dashboard", "description": "complete dashboard object from database", "optional": false, "defaultvalue": null}], "name": "create", "longname": "Dashboard.create", "scope": "route"}], "data": {"dependencies": [{"route": {"name": "/api/bucket/:id/data/dependencies/", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "app_name", "type": "string", "description": "the application name", "optional": false, "defaultvalue": null}, {"name": "server_name", "type": "string", "description": "filter by server name", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}, {"type": "400", "description": "missing parameters", "optional": false}], "response": [{"name": ".", "type": "array", "description": "recorded dependencies", "optional": false, "defaultvalue": null}], "examples": ["km.data.dependencies.retrieve(bucket._id, {\n   app_name: 'my_api'\n })"], "name": "retrieve", "longname": "Data.dependencies.retrieve", "scope": "route"}], "events": [{"route": {"name": "/api/bucket/:id/data/events", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "event_name", "type": "string", "description": "the event name to retrieve", "optional": false, "defaultvalue": null}, {"name": "start", "type": "date", "description": "from which date to retrieve events", "optional": true, "defaultvalue": null}, {"name": "end", "type": "date", "description": "to which date to retrieve events", "optional": true, "defaultvalue": null}, {"name": "app_name", "type": "string", "description": "filter events by app source", "optional": true, "defaultvalue": null}, {"name": "server_name", "type": "string", "description": "filter events by server source", "optional": true, "defaultvalue": null}, {"name": "limit", "type": "number", "description": "limit the number of events to retrieve", "optional": true, "defaultvalue": 100}, {"name": "offset", "type": "number", "description": "offset research by X", "optional": true, "defaultvalue": 0}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "400", "description": "invalid parameters", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of events", "optional": false, "defaultvalue": null}], "name": "retrieve", "longname": "Data.events.retrieve", "scope": "route"}, {"route": {"name": "/api/bucket/:id/data/eventsKeysByApp", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "400", "description": "invalid parameters", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of object representing events emitted for each application name", "optional": false, "defaultvalue": null}], "name": "retrieveMetadatas", "longname": "Data.events.retrieveMetadatas", "scope": "route"}, {"route": {"name": "/api/bucket/:id/data/events/stats", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "event_name", "type": "string", "description": "the event name to retrieve", "optional": false, "defaultvalue": null}, {"name": "app_name", "type": "string", "description": "filter events by app source", "optional": true, "defaultvalue": null}, {"name": "server_name", "type": "string", "description": "filter events by server source", "optional": true, "defaultvalue": null}, {"name": "days", "type": "number", "description": "limit the number of days of data", "optional": true, "defaultvalue": 2}, {"name": "interval", "type": "string", "description": "interval of time between two point", "optional": true, "defaultvalue": "minute"}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "400", "description": "invalid parameters", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of point (each point is one dimensional array, X are at 0 and Y at 1)", "optional": false, "defaultvalue": null}], "name": "retrieveHistogram", "longname": "Data.events.retrieveHistogram", "scope": "route"}, {"route": {"name": "/api/bucket/:id/data/events/delete_all", "type": "DELETE"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully deleted data", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of object representing events emitted for each application name", "optional": false, "defaultvalue": null}], "name": "deleteAll", "longname": "Data.events.deleteAll", "scope": "route"}], "exceptions": [{"route": {"name": "/api/bucket/:id/data/exceptions", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "server_name", "type": "string", "description": "filter exceptions by server source", "optional": true, "defaultvalue": null}, {"name": "app_name", "type": "string", "description": "filter exceptions by app source", "optional": true, "defaultvalue": null}, {"name": "before", "type": "string", "description": "filter out exceptions older than X (in minutes)", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of exceptions", "optional": false, "defaultvalue": null}], "name": "retrieve", "longname": "Data.exceptions.retrieve", "scope": "route"}, {"route": {"name": "/api/bucket/:id/data/exceptions/summary", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of object containing exceptions for each application for each server", "optional": false, "defaultvalue": null}], "name": "retrieve<PERSON><PERSON><PERSON>y", "longname": "Data.exceptions.retrieveSummary", "scope": "route"}, {"route": {"name": "/api/bucket/:id/data/exceptions/delete_all", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "name": "deleteAll", "longname": "Data.exceptions.deleteAll", "scope": "route"}, {"route": {"name": "/api/bucket/:id/data/exceptions/delete", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "identifier", "type": "string", "description": "exception identifier", "optional": true, "defaultvalue": null}, {"name": "app_name", "type": "string", "description": "the application on which exception happened", "optional": true, "defaultvalue": null}, {"name": "server_name", "type": "string", "description": "the server on which exception happened", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}, {"type": "400", "description": "missing/invalid parameters", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of deleted exceptions", "optional": false, "defaultvalue": null}], "name": "delete", "longname": "Data.exceptions.delete", "scope": "route"}], "issues": [{"route": {"name": "/api/bucket/:id/data/issues/list", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "server_name", "type": "string", "description": "filter exceptions by server source", "optional": true, "defaultvalue": null}, {"name": "app_name", "type": "string", "description": "filter exceptions by app source needed if initiator+source not provided", "optional": true, "defaultvalue": null}, {"name": "before", "type": "string", "description": "exclude exceptions older than 'before' minutes", "optional": true, "defaultvalue": null}, {"name": "initiator", "type": "string", "description": "filter exceptions by initiator (node/golang/browser/webcheck...) needed with source", "optional": true, "defaultvalue": null}, {"name": "source", "type": "string", "description": "filter exceptions by source (browser app id, webcheck id...)", "optional": true, "defaultvalue": null}, {"name": "tags", "type": "array", "description": "array of string to filter tags", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of exceptions", "optional": false, "defaultvalue": null}], "name": "list", "longname": "Data.issues.list", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/data/issues/occurrences/:identifier", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":identifier", "type": "string", "description": "issue identifier", "optional": false}], "query": [{"name": "from", "type": "number", "description": "", "optional": true, "defaultvalue": null}, {"name": "search_after", "type": "number", "description": "", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of occurrences id", "optional": false, "defaultvalue": null}], "name": "listOccurencesForIdentifier", "longname": "Data.issues.listOccurencesForIdentifier", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/data/issues/replay/:uuid", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":uuid", "type": "string", "description": "replay id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": "replay", "type": "string", "description": "", "optional": false, "defaultvalue": null}], "name": "getReplay", "longname": "Data.issues.getReplay", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/data/issues/histogram", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "app_name", "type": "string", "description": "a specific app name", "optional": true, "defaultvalue": null}, {"name": "start", "type": "string", "description": "ignore issue before this date", "optional": true, "defaultvalue": null}, {"name": "identifier", "type": "string", "description": "a specific issue identifier", "optional": true, "defaultvalue": null}, {"name": "interval", "type": "string", "description": "ignore issue before this date", "optional": true, "defaultvalue": null}, {"name": "end", "type": "string", "description": "ignore issue after this date", "optional": true, "defaultvalue": null}, {"name": "includeFixed", "type": "boolean", "description": "choose to ignore or not the fixed occurences", "optional": true, "defaultvalue": false}, {"name": "initiator", "type": "string", "description": "filter exceptions by initiator (node/golang/browser/webcheck...) needed with source", "optional": true, "defaultvalue": null}, {"name": "source", "type": "string", "description": "filter exceptions by source (browser app id, webcheck id...)", "optional": true, "defaultvalue": null}, {"name": "tags", "type": "array", "description": "array of string to filter tags", "optional": true, "defaultvalue": null}, {"name": "includeEmptyDocs", "type": "boolean", "description": "add empty docs", "optional": true, "defaultvalue": false}, {"name": "invertedTags", "type": "boolean", "description": "filter issue without tags", "optional": true, "defaultvalue": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of object containing exceptions for each application for each server", "optional": false, "defaultvalue": null}], "name": "retrieveHistogram", "longname": "Data.issues.retrieveHistogram", "scope": "route"}, {"route": {"name": "/api/bucket/:id/data/issues/ocurrences", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "identifier", "type": "object", "description": "find occurence by using an issue identifier", "optional": true, "defaultvalue": null}, {"name": "occurrence_id", "type": "object", "description": "find ocurrence by his id", "optional": true, "defaultvalue": null}, {"name": "includeFixed", "type": "boolean", "description": "choose to ignore or not the fixed occurences", "optional": true, "defaultvalue": false}, {"name": "limit", "type": "number", "description": "limit", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of object containing ocurrences", "optional": false, "defaultvalue": null}], "name": "findOccurences", "longname": "Data.issues.findOccurences", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/data/issues/search", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "message", "type": "string", "description": "find occurence that match this message", "optional": false, "defaultvalue": null}, {"name": "includeFixed", "type": "boolean", "description": "choose to ignore or not the fixed occurences", "optional": true, "defaultvalue": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of object containing exceptions for each application for each server", "optional": false, "defaultvalue": null}], "name": "search", "longname": "Data.issues.search", "scope": "route"}, {"route": {"name": "/api/bucket/:id/data/issues/summary/:aggregateBy", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":aggregateBy", "type": "string", "description": "servers, apps, initiators or sources", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": ".", "type": "array", "description": "issues count aggregated", "optional": false, "defaultvalue": null}], "name": "summary", "longname": "Data.issues.summary", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/data/issues", "type": "DELETE"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "query": [{"name": "app_name", "type": "string", "description": "an specific application to delete application", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "name": "deleteAll", "longname": "Data.issues.deleteAll", "scope": "route"}, {"route": {"name": "/api/bucket/:id/data/issues/:identifier", "type": "DELETE"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":identifier", "type": "string", "description": "the identifier of issue that you want to delete", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}, {"type": "400", "description": "missing/invalid parameters", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of deleted exceptions", "optional": false, "defaultvalue": null}], "name": "delete", "longname": "Data.issues.delete", "scope": "route"}], "logs": [{"route": {"name": "/api/bucket/:id/data/logs", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "app_name", "type": "string", "description": "the application name", "optional": true, "defaultvalue": null}, {"name": "server_name", "type": "string", "description": "filter by server name", "optional": true, "defaultvalue": null}, {"name": "before", "type": "string", "description": "only search log oldest than <before>", "optional": true, "defaultvalue": null}, {"name": "after", "type": "string", "description": "only search log newer than <after>", "optional": true, "defaultvalue": null}, {"name": "size", "type": "integer", "description": "line limit, default to 100", "optional": true, "defaultvalue": null}, {"name": "from", "type": "integer", "description": "offset limit", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}, {"type": "400", "description": "missing parameters", "optional": false}], "response": [{"name": ".", "type": "array", "description": "recorded dependencies", "optional": false, "defaultvalue": null}], "examples": ["km.data.logs.retrieve(bucket._id, {\n   app_name: 'my_api'\n })"], "name": "retrieve", "longname": "Data.logs.retrieve", "scope": "route"}, {"route": {"name": "/api/bucket/:id/data/logs/histogram", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "app_name", "type": "object", "description": "a specific app name", "optional": true, "defaultvalue": null}, {"name": "start", "type": "object", "description": "ignore log before this date", "optional": true, "defaultvalue": null}, {"name": "interval", "type": "object", "description": "ignore log before this date", "optional": true, "defaultvalue": null}, {"name": "end", "type": "object", "description": "ignore log after this date", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of object containing exceptions for each application for each server", "optional": false, "defaultvalue": null}], "name": "retrieveHistogram", "longname": "Data.logs.retrieveHistogram", "scope": "route"}], "metrics": [{"route": {"name": "/api/bucket/:id/data/metrics/aggregations", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "aggregations", "type": "object", "description": "array of aggregations to compute", "optional": false, "defaultvalue": null}, {"name": "aggregations[].name", "type": "string", "description": "the name of metric to compute the graph", "optional": false, "defaultvalue": null}, {"name": "aggregations[].types", "type": "array", "description": "type of aggregation (e.g. ['histogram', 'servers'])", "optional": false, "defaultvalue": null}, {"name": "aggregations[].start", "type": "date", "description": "oldest documents to aggregate on", "optional": false, "defaultvalue": null}, {"name": "aggregations[].end", "type": "date", "description": "newest documents to aggregate on", "optional": true, "defaultvalue": null}, {"name": "aggregations[].apps", "type": "array", "description": "filter source applications to aggregate on", "optional": true, "defaultvalue": null}, {"name": "aggregations[].interval", "type": "number", "description": "interval between two points", "optional": true, "defaultvalue": null}, {"name": "aggregations[].servers", "type": "array", "description": "filter source server to aggregate on", "optional": true, "defaultvalue": null}, {"name": "aggregations[].initiator", "type": "string", "description": "filter source initiator to aggregate on", "optional": true, "defaultvalue": null}, {"name": "aggregations[].webcheck", "type": "string", "description": "filter source webcheck to aggregate on", "optional": true, "defaultvalue": null}, {"name": "aggregations[].collector", "type": "string", "description": "filter source collector to aggregate on", "optional": true, "defaultvalue": null}, {"name": "aggregations[].tags", "type": "array", "description": "filter tags to aggregate on", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": ".", "type": "array", "description": "aggregations", "optional": false, "defaultvalue": null}], "examples": ["// Example #1: Retrieve HTTP metrics of app INTERACTION, WEB-API, WORKER from all servers\n km.data.metrics.retrieveAggregations(bucket._id, {\n  aggregations: [\n    {\n     'start': 'now-5m',\n     'apps': ['INTERACTION', 'WEB-API', 'WORKER'],\n     'types': ['histogram', 'apps', 'servers'],\n     'name': 'HTTP'\n    }\n  ]\n})\n\n // Example #2: Retrieve HTTP metrics of ALL apps from all servers\n km.data.metrics.retrieveAggregations(bucket._id, {\n  aggregations: [\n    {\n     'start': 'now-1d',\n     'types': ['histogram', 'apps', 'servers'],\n     'name': 'HTTP'\n    }\n  ]\n})"], "name": "retrieveAggregations", "longname": "Data.metrics.retrieveAggregations", "scope": "route"}, {"route": {"name": "/api/bucket/:id/data/metrics/histogram", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "app_name", "type": "string", "description": "filter probes by app source", "optional": true, "defaultvalue": null}, {"name": "server_name", "type": "string", "description": "filter probes by server source", "optional": true, "defaultvalue": null}, {"name": "interval", "type": "string", "description": "interval of time between two point", "optional": true, "defaultvalue": "minute"}, {"name": "before", "type": "string", "description": "filter out probes that are after X minute", "optional": true, "defaultvalue": 60}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": "server_name", "type": "object", "description": "", "optional": false, "defaultvalue": null}, {"name": "server_name.app_name", "type": "object", "description": "", "optional": false, "defaultvalue": null}, {"name": "server_name.app_name.metrics", "type": "object", "description": "", "optional": false, "defaultvalue": null}, {"name": "server_name.app_name.metrics.agg_type", "type": "string", "description": "the type of aggregation for this probe", "optional": false, "defaultvalue": null}, {"name": "server_name.app_name.metrics_name.timestamps_and_stats", "type": "array", "description": "array of point", "optional": false, "defaultvalue": null}], "name": "retrieveHistogram", "longname": "Data.metrics.retrieveHistogram", "scope": "route"}, {"route": {"name": "/api/bucket/:id/data/metrics/histogramPrecise", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "probe", "type": "string", "description": "probe name", "optional": false, "defaultvalue": null}, {"name": "app", "type": "string", "description": "filter probes by app source", "optional": false, "defaultvalue": null}, {"name": "server", "type": "string", "description": "filter probes by server source", "optional": false, "defaultvalue": null}, {"name": "after", "type": "string", "description": "interval of time between two point (now-5d, now-5m...)", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": "Array", "type": "array", "description": "of points", "optional": false, "defaultvalue": null}], "name": "retrieveHistogramPrecise", "longname": "Data.metrics.retrieveHistogramPrecise", "scope": "route"}, {"route": {"name": "/api/bucket/:id/data/metrics/list", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "servers", "type": "object", "description": "filter metrics by app name", "optional": true, "defaultvalue": null}, {"name": "apps", "type": "object", "description": "filter metrics by server name", "optional": true, "defaultvalue": null}, {"name": "initiator", "type": "string", "description": "filter metrics by a specific initiator", "optional": true, "defaultvalue": null}, {"name": "source", "type": "string", "description": "filter metrics by a specific source", "optional": true, "defaultvalue": null}, {"name": "collector", "type": "string", "description": "filter metrics by a specific collector", "optional": true, "defaultvalue": null}, {"name": "webcheck", "type": "string", "description": "filter metrics by a specific webcheck", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "name": "retrieveList", "longname": "Data.metrics.retrieveList", "scope": "route"}, {"route": {"name": "/api/bucket/:id/data/metrics", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "app_name", "type": "string", "description": "filter metrics by app source", "optional": false, "defaultvalue": null}, {"name": "server_name", "type": "string", "description": "filter metrics by server source", "optional": true, "defaultvalue": null}, {"name": "before", "type": "string", "description": "filter out metrics that are after X minute", "optional": true, "defaultvalue": 720}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "name": "retrieveMetadatas", "longname": "Data.metrics.retrieveMetadatas", "scope": "route"}], "notifications": [{"route": {"name": "/api/bucket/:id/data/notifications", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "type", "type": "string", "description": "Type of notification", "optional": true, "defaultvalue": null}, {"name": "before", "type": "string", "description": "we search logs before this date (lower than)", "optional": true, "defaultvalue": null}, {"name": "after", "type": "string", "description": "we search logs after this date (greater than)", "optional": true, "defaultvalue": null}, {"name": "size", "type": "number", "description": "", "optional": true, "defaultvalue": null}, {"name": "from", "type": "number", "description": "", "optional": true, "defaultvalue": null}, {"name": "type", "type": "string", "description": "type of notification", "optional": true, "defaultvalue": null}, {"name": "providers", "type": "array", "description": "find notifications with this providers", "optional": true, "defaultvalue": null}, {"name": "contacts", "type": "array", "description": "find notifications with this contact", "optional": true, "defaultvalue": null}, {"name": "size", "type": "integer", "description": "line limit, default to 20", "optional": true, "defaultvalue": null}, {"name": "from", "type": "integer", "description": "offset limit", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "tags": [{"originalTitle": "reponse", "title": "reponse", "text": "{Array} . array of traces", "value": "{Array} . array of traces", "optional": false, "type": null}], "name": "list", "longname": "Data.notifications.list", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/data/notifications/:notification", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":notification", "type": "string", "description": "notification id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "tags": [{"originalTitle": "reponse", "title": "reponse", "text": "{Object} . notification", "value": "{Object} . notification", "optional": false, "type": null}], "name": "retrieve", "longname": "Data.notifications.retrieve", "scope": "route", "async": true}], "outliers": [{"route": {"name": "/api/bucket/:id/data/outliers/", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "app_name", "type": "string", "description": "the application name", "optional": true, "defaultvalue": null}, {"name": "server_name", "type": "string", "description": "filter by server name", "optional": true, "defaultvalue": null}, {"name": "start", "type": "string", "description": "only search outlier newer than <start>", "optional": true, "defaultvalue": null}, {"name": "end", "type": "string", "description": "only search outlier older than <end>", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}, {"type": "400", "description": "missing parameters", "optional": false}], "response": [{"name": ".", "type": "array", "description": "recorded dependencies", "optional": false, "defaultvalue": null}], "examples": ["km.data.outliers.retrieve(bucket._id, {\n   app_name: 'my_api'\n })"], "name": "retrieve", "longname": "Data.outliers.retrieve", "scope": "route"}], "processes": [{"route": {"name": "/api/bucket/:id/data/processEvents", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "app_name", "type": "string", "description": "filter events by app source", "optional": true, "defaultvalue": null}, {"name": "server_name", "type": "string", "description": "filter events by server source", "optional": true, "defaultvalue": null}, {"name": "before", "type": "string", "description": "filter out events that are after X minute", "optional": true, "defaultvalue": 60}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of process events", "optional": false, "defaultvalue": null}], "name": "retrieveEvents", "longname": "Data.processes.retrieveEvents", "scope": "route"}, {"route": {"name": "/api/bucket/:id/data/processEvents/deployments", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "app_name", "type": "string", "description": "filter events by app source", "optional": true, "defaultvalue": null}, {"name": "server_name", "type": "string", "description": "filter events by server source", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of deployments", "optional": false, "defaultvalue": null}], "name": "retrieveDeployments", "longname": "Data.processes.retrieveDeployments", "scope": "route"}], "profiling": [{"route": {"name": "/api/bucket/:id/data/profilings/:filename", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":filename", "type": "string", "description": "filename", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "400", "description": "invalid parameters", "optional": false}], "response": [{"name": ".", "type": "object", "description": "return profile data", "optional": false, "defaultvalue": null}], "name": "retrieve", "longname": "Data.profiling.retrieve", "scope": "route"}, {"route": {"name": "/api/bucket/:id/data/profilings/:filename/download", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":filename", "type": "string", "description": "filename", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "400", "description": "invalid parameters", "optional": false}], "response": [{"name": ".", "type": "file", "description": "return a file", "optional": false, "defaultvalue": null}], "name": "download", "longname": "Data.profiling.download", "scope": "route"}, {"route": {"name": "/api/bucket/:id/data/profilings", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "apps", "type": "object", "description": "", "optional": true, "defaultvalue": null}, {"name": "servers", "type": "object", "description": "", "optional": true, "defaultvalue": null}, {"name": "from", "type": "object", "description": "", "optional": true, "defaultvalue": null}, {"name": "size", "type": "object", "description": "", "optional": true, "defaultvalue": null}, {"name": "type", "type": "object", "description": "", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of object containing profilings", "optional": false, "defaultvalue": null}], "name": "list", "longname": "Data.profiling.list", "scope": "route"}, {"route": {"name": "/api/bucket/:id/data/profilings/:filename", "type": "DELETE"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":filename", "type": "string", "description": "filename", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "400", "description": "invalid parameters", "optional": false}], "response": [{"name": ".", "type": "file", "description": "return a file", "optional": false, "defaultvalue": null}], "name": "delete", "longname": "Data.profiling.delete", "scope": "route"}], "status": [{"route": {"name": "/api/bucket/:id/data/status", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "tags": [{"originalTitle": "reponse", "title": "reponse", "text": "{Array} . array of servers status", "value": "{Array} . array of servers status", "optional": false, "type": null}], "name": "retrieve", "longname": "Data.status.retrieve", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/data/status/blacklisted", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "tags": [{"originalTitle": "reponse", "title": "reponse", "text": "{Array} . array of servers status", "value": "{Array} . array of servers status", "optional": false, "type": null}], "name": "retrieveBlacklisted", "longname": "Data.status.retrieveBlacklisted", "scope": "route", "async": true}], "traces": [{"route": {"name": "/api/bucket/:id/data/traces", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "includeSpans", "type": "boolean", "description": "", "optional": true, "defaultvalue": true}, {"name": "serviceName", "type": "string", "description": "", "optional": true, "defaultvalue": null}, {"name": "limit", "type": "string", "description": "default: 10, max: 100", "optional": true, "defaultvalue": null}, {"name": "kind", "type": "string", "description": "", "optional": true, "defaultvalue": null}, {"name": "minDuration", "type": "number", "description": "", "optional": true, "defaultvalue": null}, {"name": "start", "type": "string", "description": "date", "optional": true, "defaultvalue": null}, {"name": "end", "type": "string", "description": "date", "optional": true, "defaultvalue": null}, {"name": "tags", "type": "array", "description": "Query string array like [error=500, error, ...]", "optional": true, "defaultvalue": null}, {"name": "orderBy", "type": "string", "description": "Default: newest, enum: oldest, newest, shortest, longest", "optional": true, "defaultvalue": null}, {"name": "spanName", "type": "string", "description": "", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "tags": [{"originalTitle": "reponse", "title": "reponse", "text": "{Array} . array of traces", "value": "{Array} . array of traces", "optional": false, "type": null}], "name": "list", "longname": "Data.traces.list", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/data/traces/:trace", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}, {"name": ":trace", "type": "string", "description": "trace id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "tags": [{"originalTitle": "reponse", "title": "reponse", "text": "{Object} . trace", "value": "{Object} . trace", "optional": false, "type": null}], "name": "retrieve", "longname": "Data.traces.retrieve", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/data/traces/services", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": "services,", "type": "object", "description": "spans names", "optional": false, "defaultvalue": null}], "name": "getServices", "longname": "Data.traces.getServices", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/data/traces/tags", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": "tags", "type": "array", "description": "", "optional": false, "defaultvalue": null}], "name": "getTags", "longname": "Data.traces.getTags", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/data/traces/histogram/tag", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "tag", "type": "string", "description": "", "optional": false, "defaultvalue": null}, {"name": "start", "type": "string", "description": "date", "optional": true, "defaultvalue": null}, {"name": "end", "type": "string", "description": "date", "optional": true, "defaultvalue": null}, {"name": "serviceName", "type": "string", "description": "", "optional": true, "defaultvalue": null}, {"name": "spanName", "type": "string", "description": "", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": "aggregation", "type": "array", "description": "", "optional": false, "defaultvalue": null}], "name": "getHistogramByTag", "longname": "Data.traces.getHistogramByTag", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/data/traces/aggregation/tag", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "tag", "type": "string", "description": "", "optional": false, "defaultvalue": null}, {"name": "start", "type": "string", "description": "date", "optional": true, "defaultvalue": null}, {"name": "end", "type": "string", "description": "date", "optional": true, "defaultvalue": null}, {"name": "serviceName", "type": "string", "description": "", "optional": true, "defaultvalue": null}, {"name": "spanName", "type": "string", "description": "", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": "aggregation", "type": "array", "description": "", "optional": false, "defaultvalue": null}], "name": "getTagsValue", "longname": "Data.traces.getTagsValue", "scope": "route", "async": true}, {"route": {"name": "/api/bucket/:id/data/traces/aggregation/duration", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "start", "type": "string", "description": "date", "optional": true, "defaultvalue": null}, {"name": "end", "type": "string", "description": "date", "optional": true, "defaultvalue": null}, {"name": "serviceName", "type": "string", "description": "", "optional": true, "defaultvalue": null}, {"name": "spanName", "type": "string", "description": "", "optional": true, "defaultvalue": null}, {"name": "tags", "type": "array", "description": "", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": "aggregation", "type": "array", "description": "", "optional": false, "defaultvalue": null}], "name": "getDurationAvg", "longname": "Data.traces.getDurationAvg", "scope": "route", "async": true}], "transactions": [{"route": {"name": "/api/bucket/:id/data/transactions/v2/histogram", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "app_name", "type": "string", "description": "filter transactions by app source", "optional": true, "defaultvalue": null}, {"name": "server_name", "type": "string", "description": "filter transactions by server source", "optional": true, "defaultvalue": null}, {"name": "interval", "type": "string", "description": "interval of time between two point", "optional": true, "defaultvalue": "minute"}, {"name": "before", "type": "string", "description": "filter out transactions that are after X minute", "optional": true, "defaultvalue": 60}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of times series containing points", "optional": false, "defaultvalue": null}], "name": "retrieveHistogram", "longname": "Data.transactions.retrieveHistogram", "scope": "route"}, {"route": {"name": "/api/bucket/:id/data/transactions/v2/summary", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "body": [{"name": "app_name", "type": "string", "description": "filter transactions by app source", "optional": true, "defaultvalue": null}, {"name": "server_name", "type": "string", "description": "filter transactions by server source", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": "server_name", "type": "object", "description": "", "optional": false, "defaultvalue": null}, {"name": "server_name.app_name", "type": "object", "description": "transaction object", "optional": false, "defaultvalue": null}], "name": "retrieve<PERSON><PERSON><PERSON>y", "longname": "Data.transactions.retrieveSummary", "scope": "route"}, {"route": {"name": "/api/bucket/:id/data/transactions/v2/delete", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "bucket id", "optional": false}], "query": [{"name": "app_name", "type": "string", "description": "filter transactions by app source", "optional": true, "defaultvalue": null}, {"name": "server_name", "type": "string", "description": "filter transactions by server source", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}], "response": [{"name": "server_name", "type": "object", "description": "", "optional": false, "defaultvalue": null}, {"name": "server_name.app_name", "type": "object", "description": "transaction object", "optional": false, "defaultvalue": null}], "name": "delete", "longname": "Data.transactions.delete", "scope": "route"}]}, "misc": [{"route": {"name": "/api/misc/changelog", "type": "GET"}, "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}, {"type": "400", "description": "Invalid params", "optional": false}], "response": [{"name": "changelog", "type": "array", "description": "articles", "optional": false, "defaultvalue": null}], "name": "listChangelogArticles", "longname": "Misc.listChangelogArticles", "scope": "route", "params": [], "authentication": false}, {"route": {"name": "/api/misc/release/pm2", "type": "GET"}, "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}, {"type": "400", "description": "Invalid params", "optional": false}], "response": [{"name": "pm2_version", "type": "string", "description": "latest version", "optional": false, "defaultvalue": null}], "name": "retrievePM2Version", "longname": "Misc.retrievePM2Version", "scope": "route", "params": [], "authentication": false}, {"route": {"name": "/api/misc/release/nodejs/:version", "type": "GET"}, "params": [{"name": ":version", "type": "string", "description": "semver version range", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}, {"type": "400", "description": "Invalid params", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of releases matching the range requested", "optional": false, "defaultvalue": null}], "name": "retrieveNodeRelease", "longname": "Misc.retrieveNodeRelease", "scope": "route", "authentication": false}, {"route": {"name": "/api/misc/plans", "type": "GET"}, "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "succesfully retrieved data", "optional": false}, {"type": "400", "description": "Invalid params", "optional": false}], "response": [{"name": ".", "type": "object", "description": "list of plans keyed by plan name", "optional": false, "defaultvalue": null}], "name": "retrievePlans", "longname": "Misc.retrievePlans", "scope": "route", "params": [], "authentication": false}, {"route": {"name": "/api/misc/stripe/retrieveCoupon", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "body": [{"name": "coupon", "type": "string", "description": "the coupon name", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "stripe error", "optional": false}, {"type": "200", "description": "succesfully retrieved the metadata", "optional": false}], "response": [{"name": "coupon", "type": "object", "description": "the coupon object", "optional": false, "defaultvalue": null}], "name": "retrieveCoupon", "longname": "Misc.retrieveCoupon", "scope": "route", "params": []}, {"route": {"name": "/api/misc/stripe/retrieveCompany", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "body": [{"name": "vat_id", "type": "string", "description": "the vat id of the company", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "stripe error", "optional": false}, {"type": "200", "description": "succesfully retrieved the metadata", "optional": false}], "response": [{"name": ".", "type": "object", "description": "metadata about company", "optional": false, "defaultvalue": null}], "name": "retrieveCompany", "longname": "Misc.retrieveCompany", "scope": "route", "params": []}, {"route": {"name": "/api/misc/stripe/retrieveVat", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "body": [{"name": "country", "type": "string", "description": "country code of the user", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "stripe error", "optional": false}, {"type": "200", "description": "succesfully retrieved the metadata", "optional": false}], "response": [{"name": "coupon", "type": "object", "description": "the coupon object", "optional": false, "defaultvalue": null}], "name": "retrieveVAT", "longname": "Misc.retrieveVAT", "scope": "route", "params": []}], "node": [{"route": {"name": "/api/node/default", "type": "GET"}, "response": [{"name": "node", "type": "object", "description": "Return node object", "optional": false, "defaultvalue": null}], "name": "getDefaultNode", "longname": "Node.getDefaultNode", "scope": "route", "params": [], "authentication": false}], "orchestration": [{"route": {"name": "/api/bucket/:id/balance", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "balancing error", "optional": false}, {"type": "403", "description": "already on new node or not premium", "optional": false}, {"type": "200", "description": "succesfully balanced the bucket", "optional": false}], "response": [{"name": "migration", "type": "object", "description": "is equal true if succesfull", "optional": false, "defaultvalue": null}], "name": "selfSend", "longname": "Orchestration.selfSend", "scope": "route", "params": []}], "tokens": [{"route": {"name": "/api/users/token/", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "successfully retrieved", "optional": false}], "response": [{"name": ".", "type": "object", "description": "array of tokens", "optional": false, "defaultvalue": null}], "name": "retrieve", "longname": "Tokens.retrieve", "scope": "route", "params": []}, {"route": {"name": "/api/users/token/:id", "type": "DELETE"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "params": [{"name": ":id", "type": "string", "description": "token id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "404", "description": "token not found", "optional": false}, {"type": "200", "description": "refresh token has been deleted and all access token that have been created with it", "optional": false}], "response": [{"name": ".", "type": "object", "description": "array of tokens", "optional": false, "defaultvalue": null}], "name": "remove", "longname": "Tokens.remove", "scope": "route"}, {"route": {"name": "/api/users/token/", "type": "PUT"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "body": [{"name": "scope", "type": "object", "description": "a valid oauth scope", "optional": false, "defaultvalue": null}], "code": [{"type": "409", "description": "the otp is already enabled for the user, you can only delete it", "optional": false}, {"type": "200", "description": "the otp can be registered for the account, return the full response", "optional": false}], "response": [{"name": ".", "type": "object", "description": "generated token", "optional": false, "defaultvalue": null}], "name": "create", "longname": "Tokens.create", "scope": "route", "params": []}], "user": {"otp": [{"route": {"name": "/api/users/otp", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "code": [{"type": "409", "description": "the otp is already enabled for the user, you can only delete it", "optional": false}, {"type": "200", "description": "the otp can be registered for the account, return the full response", "optional": false}], "response": [{"name": "user", "type": "object", "description": "user model", "optional": false, "defaultvalue": null}, {"name": "key", "type": "string", "description": "otp secret key", "optional": false, "defaultvalue": null}, {"name": "qrImage", "type": "string", "description": "url to the QrCode", "optional": false, "defaultvalue": null}], "name": "retrieve", "longname": "User.otp.retrieve", "scope": "route", "params": []}, {"route": {"name": "/api/users/otp", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "body": [{"name": "otpKey", "type": "string", "description": "secret key used to generate OTP code", "optional": false, "defaultvalue": null}, {"name": "otpToken", "type": "string", "description": "a currently valid OTP code generated with the otpKey", "optional": false, "defaultvalue": null}], "code": [{"type": "400", "description": "missing parameters", "optional": false}, {"type": "403", "description": "the code asked to add the OTP from user account is invalid", "optional": false}, {"type": "500", "description": "error from database", "optional": false}, {"type": "200", "description": "the otp has been registered for the user", "optional": false}], "name": "enable", "longname": "User.otp.enable", "scope": "route", "params": []}, {"route": {"name": "/api/users/otp", "type": "DELETE"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "query": [{"name": "otpToken", "type": "string", "description": "a currently valid OTP code", "optional": false, "defaultvalue": null}], "code": [{"type": "400", "description": "missing parameters", "optional": false}, {"type": "403", "description": "the code asked to remove the OTP from user account is invalid", "optional": false}, {"type": "500", "description": "error from database", "optional": false}, {"type": "200", "description": "the otp has been deleted for the user", "optional": false}], "name": "disable", "longname": "User.otp.disable", "scope": "route", "params": []}], "providers": [{"route": {"name": "/api/users/integrations", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "code": [{"type": "200", "description": "succesfully retrieved providers", "optional": false}], "response": [{"name": ".", "type": "array", "description": "array of providers for user account", "optional": false, "defaultvalue": null}], "name": "retrieve", "longname": "User.providers.retrieve", "scope": "route", "params": []}, {"route": {"name": "/api/users/integrations", "type": "POST"}, "authentication": true, "body": [{"name": "name", "type": "string", "description": "the provider name", "optional": false, "defaultvalue": null}], "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "code": [{"type": "400", "description": "invalid parameters", "optional": false}, {"type": "403", "description": "the user already have this provider", "optional": false}, {"type": "200", "description": "succesfully added the provider", "optional": false}], "name": "add", "longname": "User.providers.add", "scope": "route", "params": []}, {"route": {"name": "/api/users/integrations/:name", "type": "DELETE"}, "authentication": true, "params": [{"name": ":name", "type": "string", "description": "the provider name", "optional": false}], "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "code": [{"type": "400", "description": "invalid parameters or provider isn't implemented", "optional": false}, {"type": "403", "description": "the provider isn't enabled", "optional": false}, {"type": "200", "description": "succesfully removed the provider", "optional": false}], "name": "remove", "longname": "User.providers.remove", "scope": "route"}], "default": [{"name": "retrieve", "route": {"name": "/api/users/isLogged", "type": "GET"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "code": [{"type": "200", "description": "the user has been retrieved", "optional": false}], "response": [{"name": "user", "type": "object", "description": "user model", "optional": false, "defaultvalue": null}], "longname": "User.retrieve", "scope": "route"}, {"route": {"name": "/api/users/show/:id", "type": "GET"}, "params": [{"name": ":id", "type": "string", "description": "user id", "optional": false}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "400", "description": "invalid parameters (no id provided)", "optional": false}, {"type": "404", "description": "no user account where found", "optional": false}, {"type": "200", "description": "the mail has been sent to the provided email", "optional": false}], "response": [{"name": "String", "type": "", "description": "email user email", "optional": false, "defaultvalue": null}, {"name": "String", "type": "", "description": "username user pseudo", "optional": false, "defaultvalue": null}], "name": "show", "longname": "User.show", "scope": "route", "authentication": false}, {"route": {"name": "/api/users/update", "type": "POST"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "body": [{"name": "username", "type": "string", "description": "", "optional": true, "defaultvalue": null}, {"name": "email", "type": "string", "description": "", "optional": true, "defaultvalue": null}, {"name": "old_password", "type": "string", "description": "", "optional": true, "defaultvalue": null}, {"name": "new_password", "type": "string", "description": "", "optional": true, "defaultvalue": null}, {"name": "info", "type": "object", "description": "", "optional": true, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "400", "description": "missing parameters, no data to update", "optional": false}, {"type": "403", "description": "when updating the password, it need a new one", "optional": false}, {"type": "406", "description": "when updating the password, the old one is false", "optional": false}, {"type": "409", "description": "when updating email or username\n another user already have one of those two", "optional": false}, {"type": "200", "description": "succesfully updated the card", "optional": false}], "response": [{"name": ".", "type": "object", "description": "user object", "optional": false, "defaultvalue": null}], "name": "update", "longname": "User.update", "scope": "route", "params": []}, {"route": {"name": "/api/users/delete", "type": "DELETE"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "database error", "optional": false}, {"type": "403", "description": "permission denied (hold buckets)", "optional": false}, {"type": "200", "description": "succesfully deleted the user", "optional": false}], "response": [{"name": ".", "type": "object", "description": "user object", "optional": false, "defaultvalue": null}], "name": "delete", "longname": "User.delete", "scope": "route", "params": []}]}, "auth": [{"name": "retrieveToken", "route": {"name": "/api/oauth/token", "type": "POST"}, "service": {"name": "OAUTH"}, "body": [{"name": "client_id", "type": "string", "description": "the public id of your oauth application", "optional": false, "defaultvalue": null}, {"name": "refresh_token", "type": "string", "description": "refresh token you retrieved via authorize endpoint", "optional": false, "defaultvalue": null}, {"name": "grant_type", "type": "string", "description": "", "optional": false, "defaultvalue": "refresh_token"}], "code": [{"type": "400", "description": "invalid parameters (missing or not correct)", "optional": false}], "response": [{"name": "access_token", "type": "string", "description": "a fresh access_token", "optional": false, "defaultvalue": null}, {"name": "refresh_token", "type": "string", "description": "the refresh token you used", "optional": false, "defaultvalue": null}, {"name": "expire_at", "type": "string", "description": "UTC date at which the token will be considered\n as invalid", "optional": false, "defaultvalue": null}, {"name": "token_type", "type": "string", "description": "the type of token to use, for now its always Bearer", "optional": false, "defaultvalue": null}], "longname": "<PERSON><PERSON><PERSON>", "scope": "route", "authentication": false}, {"name": "requestNewPassword", "route": {"name": "/api/oauth/reset_password", "type": "POST"}, "service": {"name": "OAUTH"}, "body": [{"name": "email", "type": "string", "description": "email of the account that want a password reset", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "the database failed to register the token to reset the mail", "optional": false}, {"type": "400", "description": "missing parameters", "optional": false}, {"type": "404", "description": "no user account where found with the provided email", "optional": false}, {"type": "200", "description": "the mail has been sent to the provided email", "optional": false}], "longname": "Auth.requestNewPassword", "scope": "route", "authentication": false}, {"name": "sendEmailLink", "route": {"name": "/api/oauth/send_email_link", "type": "POST"}, "service": {"name": "OAUTH"}, "code": [{"type": "500", "description": "the database failed to register the token to reset the mail", "optional": false}, {"type": "401", "description": "need to authenticated", "optional": false}, {"type": "200", "description": "the mail has been sent to the provided email", "optional": false}], "longname": "Auth.sendEmailLink", "scope": "route", "authentication": false}, {"name": "validEmail", "route": {"name": "/api/oauth/valid_email/:token", "type": "GET"}, "params": [{"description": "the token to validate the account", "name": ":token", "optional": false, "type": null}], "service": {"name": "OAUTH"}, "code": [{"type": "500", "description": "the database failed to valid email", "optional": false}, {"type": "404", "description": "need to authenticated", "optional": false}, {"type": "301", "description": "the email has been valided", "optional": false}], "longname": "Auth.validEmail", "scope": "route", "authentication": false}, {"route": {"name": "/api/oauth/register", "type": "GET"}, "service": {"name": "OAUTH"}, "body": [{"name": "username", "type": "string", "description": "", "optional": false, "defaultvalue": null}, {"name": "email", "type": "string", "description": "", "optional": false, "defaultvalue": null}, {"name": "password", "type": "string", "description": "", "optional": false, "defaultvalue": null}, {"name": "role", "type": "string", "description": "job title in user company", "optional": true, "defaultvalue": null}, {"name": "company", "type": "string", "description": "company name", "optional": true, "defaultvalue": null}, {"name": "accept_terms", "type": "integer", "description": "", "optional": false, "defaultvalue": null}], "code": [{"type": "500", "description": "either the registeration of new user is disabled or\nthe database failed to register the user", "optional": false}, {"type": "409", "description": "the user field are already used by another user", "optional": false}, {"type": "200", "description": "the user has been created", "optional": false}], "response": [{"name": "user", "type": "object", "description": "user model", "optional": false, "defaultvalue": null}, {"name": "access_token", "type": "object", "description": "access token issued for the user", "optional": false, "defaultvalue": null}, {"name": "refreshToken", "type": "object", "description": "refresh token issued for the user", "optional": false, "defaultvalue": null}], "name": "register", "longname": "Auth.register", "scope": "route", "authentication": false}, {"route": {"name": "/api/oauth/revoke", "type": "POST"}, "service": {"name": "OAUTH"}, "authentication": true, "header": [{"name": "Authorization", "type": "string", "description": "bearer access token issued for the user", "optional": false, "defaultvalue": null}], "code": [{"type": "404", "description": "token not found", "optional": false}, {"type": "500", "description": "database error", "optional": false}, {"type": "200", "description": "the token has been succesfully deleted,\n if there was access token generated with this token, they\n have been deleted too", "optional": false}], "name": "revoke", "longname": "Auth.revoke", "scope": "route"}]}