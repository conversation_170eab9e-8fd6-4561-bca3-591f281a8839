{"name": "promptly", "version": "2.2.0", "description": "Simple command line prompting utility", "main": "index.js", "dependencies": {"read": "^1.0.4"}, "devDependencies": {"async": "^2.0.0", "expect.js": "^0.3.1", "mocha": "^3.0.2"}, "scripts": {"test": "mocha --bail"}, "repository": {"type": "git", "url": "git://github.com/IndigoUnited/node-promptly"}, "bugs": {"url": "http://github.com/IndigoUnited/node-promptly/issues"}, "keywords": ["prompt", "choose", "choice", "cli", "command", "line"], "author": "IndigoUnited <<EMAIL>> (http://indigounited.com)", "license": "MIT"}