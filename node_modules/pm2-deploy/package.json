{"name": "pm2-deploy", "version": "1.0.2", "description": "Deployment system for PM2", "main": "deploy.js", "files": ["deploy.js", "deploy"], "engines": {"node": ">=4.0.0"}, "repository": {"type": "git", "url": "https://github.com/Unitech/pm2-deploy.git"}, "scripts": {"lint": "eslint \"**/*.js\"", "test": "mocha", "docs": "documentation readme ./deploy.js --section=API"}, "dependencies": {"run-series": "^1.1.8", "tv4": "^1.3.0"}, "devDependencies": {"better-assert": "^1.0.2", "documentation": "^11.0.0", "eslint": "^5.16.0", "eslint-config-semistandard": "^13.0.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "mocha": "^5.2.0", "should": "^13.2.3"}, "author": "<PERSON>", "license": "MIT"}