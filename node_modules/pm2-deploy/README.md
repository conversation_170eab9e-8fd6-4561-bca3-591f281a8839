# Deploy system for PM2

This is the module that allows to do `pm2 deploy`.
Documentation: http://pm2.keymetrics.io/docs/usage/deployment/

[![build status](https://badgen.net/travis/Unitech/pm2/master)](https://travis-ci.org/Unitech/pm2-deploy) [![npm package version](https://badgen.net/npm/v/pm2-deploy)](https://npm.im/pm2-deploy) [![install size](https://badgen.net/packagephobia/install/pm2-deploy)](https://packagephobia.now.sh/result?p=pm2-deploy) [![github license](https://badgen.net/github/license/Unitech/pm2-deploy)](https://github.com/Unitech/pm2-deploy/blob/master/LICENSE) [![js semistandard style](https://badgen.net/badge/code%20style/semistandard/pink)](https://github.com/Flet/semistandard)

## Instalation

    $ npm install pm2-deploy

## Programmatic Usage

```js
var deployForEnv = require('pm2-deploy').deployForEnv;

// Define deploy configuration with target environments
var deployConfig = {
  prod: {
    user: 'node',
    host: '**************',
    ref: 'origin/master',
    repo: '**************:Unitech/eip-vitrine.git',
    path: '/var/www/test-deploy'
  },
  dev: {
    user: 'node',
    host: '**************',
    ref: 'origin/master',
    repo: '**************:Unitech/eip-vitrine.git',
    path: '/var/www/test-dev'
  }
};

// Invoke deployment for `dev` environment
deployForEnv(deployConfig, 'dev', [], function (err, args) {
  if (err) {
    console.error('Deploy failed:', err.message);
    return console.error(err.stack);
  }
  console.log('Success!');
});

// Rollback `prod` environment
deployForEnv(deployConfig, 'prod', ['revert', 1], function (err, args) {
  if (err) {
    console.error('Rollback failed:', err.message);
    return console.error(err.stack);
  }
  console.log('Success!');
});
```

## API

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

#### Table of Contents

-   [deployForEnv](#deployforenv)
    -   [Parameters](#parameters)
-   [DeployCallback](#deploycallback)
    -   [Parameters](#parameters-1)

### deployForEnv

Deploy to a single environment

#### Parameters

-   `deployConfig` **[object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)** object containing deploy configs for all environments
-   `env` **[string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String)** the name of the environment to deploy to
-   `args` **[array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)** custom deploy command-line arguments
-   `cb` **[DeployCallback](#deploycallback)** done callback

Returns **[boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** return value is always `false`

### DeployCallback

Type: [Function](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function)

#### Parameters

-   `error` **[Error](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Error)** deployment error
-   `args` **[array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)** custom command-line arguments provided to deploy
