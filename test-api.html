<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test Page</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <h1>🔧 API Diagnostic Tool</h1>
    <p>Use this page to test if the logging API endpoints are working correctly.</p>

    <div class="test-section">
        <h3>1. Health Check</h3>
        <button onclick="testHealthCheck()">Check Server Health</button>
        <div id="healthResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. Test Session Creation</h3>
        <button onclick="testSessionCreation()">Create Session</button>
        <div id="sessionResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. Test Interaction Logging</h3>
        <button onclick="testInteractionLogging()">Send Test Interaction</button>
        <div id="interactionResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. Test Batch Logging</h3>
        <button onclick="testBatchLogging()">Send Batch Interactions</button>
        <div id="batchResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>5. Test Sessions List</h3>
        <button onclick="testSessionsList()">Get Active Sessions</button>
        <div id="sessionsResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>6. Test Admin Logs</h3>
        <button onclick="testAdminLogs()">Get Recent Logs</button>
        <div id="logsResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>7. Network Information</h3>
        <button onclick="showNetworkInfo()">Show Network Info</button>
        <div id="networkResult" class="result"></div>
    </div>

    <script>
        let testSessionId = null;

        function displayResult(elementId, success, message, data = null) {
            const element = document.getElementById(elementId);
            element.className = `result ${success ? 'success' : 'error'}`;
            element.innerHTML = `
                <strong>${success ? '✅ Success' : '❌ Error'}:</strong> ${message}
                ${data ? `<pre>${JSON.stringify(data, null, 2)}</pre>` : ''}
            `;
        }

        async function testHealthCheck() {
            try {
                console.log('Testing server health...');
                const response = await fetch('/api/health');

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                displayResult('healthResult', true, 'Server is healthy', data);
            } catch (error) {
                displayResult('healthResult', false, error.message);
                console.error('Health check failed:', error);
            }
        }

        async function testSessionCreation() {
            try {
                console.log('Testing session creation...');
                const response = await fetch('/api/session/create', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                testSessionId = data.sessionId;
                displayResult('sessionResult', true, 'Session created successfully', data);
            } catch (error) {
                displayResult('sessionResult', false, error.message);
                console.error('Session creation failed:', error);
            }
        }

        async function testInteractionLogging() {
            if (!testSessionId) {
                displayResult('interactionResult', false, 'Please create a session first');
                return;
            }

            try {
                const response = await fetch('/api/interaction/log', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        sessionId: testSessionId,
                        interaction: {
                            type: 'test',
                            message: 'API diagnostic test',
                            timestamp: new Date().toISOString()
                        }
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                displayResult('interactionResult', true, 'Interaction logged successfully', data);
            } catch (error) {
                displayResult('interactionResult', false, error.message);
                console.error('Interaction logging failed:', error);
            }
        }

        async function testBatchLogging() {
            if (!testSessionId) {
                displayResult('batchResult', false, 'Please create a session first');
                return;
            }

            try {
                const interactions = [
                    { type: 'click', x: 100, y: 200, element: 'test-button' },
                    { type: 'keydown', key: 'Enter', code: 'Enter' },
                    { type: 'mousemove', x: 150, y: 250 }
                ];

                const response = await fetch('/api/interaction/batch', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        sessionId: testSessionId,
                        interactions: interactions
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                displayResult('batchResult', true, 'Batch interactions logged successfully', data);
            } catch (error) {
                displayResult('batchResult', false, error.message);
                console.error('Batch logging failed:', error);
            }
        }

        async function testSessionsList() {
            try {
                const response = await fetch('/api/sessions');

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                displayResult('sessionsResult', true, `Found ${data.length} active sessions`, data);
            } catch (error) {
                displayResult('sessionsResult', false, error.message);
                console.error('Sessions list failed:', error);
            }
        }

        async function testAdminLogs() {
            try {
                const response = await fetch('/admin/logs?limit=10');

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                displayResult('logsResult', true, `Retrieved ${data.logs.length} log entries`, data);
            } catch (error) {
                displayResult('logsResult', false, error.message);
                console.error('Admin logs failed:', error);
            }
        }

        function showNetworkInfo() {
            const info = {
                currentURL: window.location.href,
                hostname: window.location.hostname,
                port: window.location.port,
                protocol: window.location.protocol,
                userAgent: navigator.userAgent,
                onLine: navigator.onLine,
                cookieEnabled: navigator.cookieEnabled
            };

            displayResult('networkResult', true, 'Network information retrieved', info);
        }

        // Auto-run network info and health check on page load
        window.addEventListener('load', () => {
            showNetworkInfo();
            testHealthCheck();
        });
    </script>
</body>
</html>
