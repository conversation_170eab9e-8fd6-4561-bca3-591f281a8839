#!/bin/bash

# Robust deploy script that handles SSH connection issues
# Usage: ./deploy-robust.sh <username> <password> <server>

# Check if correct number of arguments provided
if [ $# -ne 3 ]; then
    echo "Usage: $0 <username> <password> <server>"
    echo "Example: $0 myuser mypass example.com"
    exit 1
fi

USERNAME=$1
PASSWORD=$2
SERVER=$3
REMOTE_DIR="/var/www/bsod"
PORT=8081

echo "🚀 Starting robust deployment to $SERVER..."

# Check if sshpass is installed
if ! command -v sshpass &> /dev/null; then
    echo "❌ Error: sshpass is not installed. Please install it first."
    echo "Ubuntu/Debian: sudo apt-get install sshpass"
    exit 1
fi

# Function to run SSH command with retry
run_ssh_command() {
    local command="$1"
    local description="$2"
    local max_retries=3
    local retry=0
    
    echo "🔄 $description..."
    
    while [ $retry -lt $max_retries ]; do
        if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 -o ServerAliveInterval=5 "$USERNAME@$SERVER" "$command"; then
            echo "✅ $description completed successfully"
            return 0
        else
            retry=$((retry + 1))
            echo "⚠️  Attempt $retry failed, retrying..."
            sleep 2
        fi
    done
    
    echo "❌ $description failed after $max_retries attempts"
    return 1
}

# Step 1: Test connection
echo "Step 1: Testing SSH connection..."
if ! run_ssh_command "echo 'Connection test successful'" "Connection test"; then
    echo "❌ Cannot connect to server. Please check credentials and network."
    exit 1
fi

# Step 2: Create directory
echo "Step 2: Creating remote directory..."
run_ssh_command "mkdir -p $REMOTE_DIR" "Directory creation"

# Step 3: Sync files
echo "Step 3: Syncing files..."
echo "📁 Preparing files for transfer..."

# Create exclude list for rsync
cat > .deploy-exclude << 'EOL'
.git/
.gitignore
node_modules/
user-logs/
*.log
.DS_Store
Thumbs.db
.vscode/
.idea/
*.swp
*.swo
*~
deploy*.sh
.deploy-exclude
EOL

# Try rsync first, fallback to scp
if command -v rsync &> /dev/null; then
    echo "📦 Using rsync for file transfer..."
    if rsync -avz --progress --exclude-from=.deploy-exclude \
        -e "sshpass -p '$PASSWORD' ssh -o StrictHostKeyChecking=no -o ConnectTimeout=30" \
        ./ "$USERNAME@$SERVER:$REMOTE_DIR/"; then
        echo "✅ Rsync transfer completed"
    else
        echo "⚠️  Rsync failed, trying alternative method..."
        # Fallback to tar + scp
        echo "📦 Creating archive for transfer..."
        tar --exclude-from=.deploy-exclude -czf deploy-temp.tar.gz .
        
        if sshpass -p "$PASSWORD" scp -o StrictHostKeyChecking=no -o ConnectTimeout=30 \
            deploy-temp.tar.gz "$USERNAME@$SERVER:$REMOTE_DIR/"; then
            echo "✅ Archive uploaded, extracting..."
            run_ssh_command "cd $REMOTE_DIR && tar -xzf deploy-temp.tar.gz && rm deploy-temp.tar.gz" "File extraction"
        else
            echo "❌ File transfer failed"
            rm -f deploy-temp.tar.gz .deploy-exclude
            exit 1
        fi
        
        rm -f deploy-temp.tar.gz
    fi
else
    echo "📦 Using tar + scp for file transfer..."
    tar --exclude-from=.deploy-exclude -czf deploy-temp.tar.gz .
    
    if sshpass -p "$PASSWORD" scp -o StrictHostKeyChecking=no -o ConnectTimeout=30 \
        deploy-temp.tar.gz "$USERNAME@$SERVER:$REMOTE_DIR/"; then
        echo "✅ Archive uploaded, extracting..."
        run_ssh_command "cd $REMOTE_DIR && tar -xzf deploy-temp.tar.gz && rm deploy-temp.tar.gz" "File extraction"
    else
        echo "❌ File transfer failed"
        rm -f deploy-temp.tar.gz .deploy-exclude
        exit 1
    fi
    
    rm -f deploy-temp.tar.gz
fi

# Clean up
rm -f .deploy-exclude

# Step 4: Install Node.js (if needed)
echo "Step 4: Setting up Node.js..."
run_ssh_command "
    if ! command -v node &> /dev/null; then
        echo 'Installing Node.js...'
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        sudo apt-get install -y nodejs
    else
        echo 'Node.js already installed:' \$(node --version)
    fi
" "Node.js installation"

# Step 5: Install dependencies
echo "Step 5: Installing dependencies..."
run_ssh_command "
    cd $REMOTE_DIR
    if [ -f package.json ]; then
        echo 'Installing npm dependencies...'
        npm install --production
    fi
" "NPM dependencies installation"

# Step 6: Install PM2
echo "Step 6: Setting up PM2..."
run_ssh_command "
    if ! command -v pm2 &> /dev/null; then
        echo 'Installing PM2...'
        npm install -g pm2
    else
        echo 'PM2 already installed:' \$(pm2 --version)
    fi
" "PM2 installation"

# Step 7: Start application
echo "Step 7: Starting application..."
run_ssh_command "
    cd $REMOTE_DIR
    
    # Stop existing processes
    pm2 stop app-$PORT 2>/dev/null || true
    pm2 delete app-$PORT 2>/dev/null || true
    
    # Start the application
    echo 'Starting application...'
    PORT=$PORT pm2 start server.js --name app-$PORT
    
    # Save PM2 configuration
    pm2 save
    
    echo 'Application started successfully!'
" "Application startup"

# Step 8: Verify deployment
echo "Step 8: Verifying deployment..."
run_ssh_command "
    cd $REMOTE_DIR
    pm2 status
    echo ''
    echo 'Checking if application is responding...'
    sleep 3
    curl -s http://localhost:$PORT/api/health || echo 'Health check endpoint not responding yet'
" "Deployment verification"

echo ""
echo "🎉 Deployment completed!"
echo "📍 Your application should be available at: http://$SERVER:$PORT"
echo "🔧 System dashboard: http://$SERVER:$PORT/sys/dashboard"
echo "🧪 API test page: http://$SERVER:$PORT/test-api.html"
echo ""
echo "💡 If you encounter issues:"
echo "   1. Check if port $PORT is open in firewall: sudo ufw allow $PORT"
echo "   2. View application logs: pm2 logs app-$PORT"
echo "   3. Check application status: pm2 status"
echo "   4. Test API endpoints using the test page"
