#!/bin/bash

# Minimal deploy script - just upload files and start with node
# Usage: ./deploy-minimal.sh <username> <password> <server>

if [ $# -ne 3 ]; then
    echo "Usage: $0 <username> <password> <server>"
    exit 1
fi

USERNAME=$1
PASSWORD=$2
SERVER=$3
REMOTE_DIR="/var/www/bsod"
PORT=8081

echo "🚀 Minimal deployment to $SERVER..."

# Check sshpass
if ! command -v sshpass &> /dev/null; then
    echo "❌ sshpass not found. Install with: sudo apt-get install sshpass"
    exit 1
fi

# Test connection
echo "🔌 Testing connection..."
if ! sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 "$USERNAME@$SERVER" "echo 'Connected'"; then
    echo "❌ Connection failed"
    exit 1
fi

# Create directory
echo "📁 Creating directory..."
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$USERNAME@$SERVER" "mkdir -p $REMOTE_DIR"

# Upload files using tar (most reliable)
echo "📦 Uploading files..."
tar --exclude='.git' --exclude='node_modules' --exclude='user-logs' --exclude='*.log' \
    --exclude='deploy*.sh' --exclude='.DS_Store' -czf app.tar.gz .

sshpass -p "$PASSWORD" scp -o StrictHostKeyChecking=no app.tar.gz "$USERNAME@$SERVER:$REMOTE_DIR/"

if [ $? -ne 0 ]; then
    echo "❌ File upload failed"
    rm -f app.tar.gz
    exit 1
fi

rm -f app.tar.gz

# Extract and start
echo "🚀 Extracting and starting application..."
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$USERNAME@$SERVER" << 'EOF'
    cd /var/www/bsod
    
    # Extract files
    tar -xzf app.tar.gz
    rm app.tar.gz
    
    # Kill any existing node processes on this port
    pkill -f "node.*server.js" || true
    
    # Install dependencies if Node.js is available
    if command -v npm &> /dev/null; then
        echo "Installing dependencies..."
        npm install --production 2>/dev/null || echo "npm install failed, continuing..."
    fi
    
    # Start with nohup (background process)
    echo "Starting application..."
    nohup node server.js > app.log 2>&1 &
    
    # Wait a moment and check if it's running
    sleep 3
    if pgrep -f "node.*server.js" > /dev/null; then
        echo "✅ Application started successfully!"
        echo "Process ID: $(pgrep -f 'node.*server.js')"
    else
        echo "❌ Application may not have started. Check app.log for errors."
    fi
EOF

echo ""
echo "🎉 Deployment completed!"
echo "📍 Application URL: http://$SERVER:$PORT"
echo "🔧 System dashboard: http://$SERVER:$PORT/sys/dashboard"
echo ""
echo "💡 To check status:"
echo "   ssh $USERNAME@$SERVER 'cd $REMOTE_DIR && tail -f app.log'"
echo "   ssh $USERNAME@$SERVER 'pgrep -f \"node.*server.js\"'"
