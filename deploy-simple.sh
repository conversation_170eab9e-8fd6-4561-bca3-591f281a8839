#!/bin/bash

# Simple deploy script with rsync fallback to scp
# Usage: ./deploy-simple.sh <username> <password> <server>

# Check if correct number of arguments provided
if [ $# -ne 3 ]; then
    echo "Usage: $0 <username> <password> <server>"
    echo "Example: $0 myuser mypass example.com"
    exit 1
fi

USERNAME=$1
PASSWORD=$2
SERVER=$3
REMOTE_DIR="/var/www/bsod"
PORT=8081

echo "Starting deployment to $SERVER..."

# Check if sshpass is installed
if ! command -v sshpass &> /dev/null; then
    echo "Error: sshpass is not installed. Please install it first."
    echo "Ubuntu/Debian: sudo apt-get install sshpass"
    echo "CentOS/RHEL: sudo yum install sshpass"
    echo "macOS: brew install sshpass"
    exit 1
fi

echo "Step 1: Creating remote directory..."
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$USERNAME@$SERVER" "mkdir -p $REMOTE_DIR"

if [ $? -ne 0 ]; then
    echo "Error: Failed to create remote directory or connect to server"
    exit 1
fi

echo "Step 2: Transferring files..."

# Try rsync if available
if command -v rsync &> /dev/null; then
    echo "Using rsync for efficient transfer..."
    
    # Create exclude list
    EXCLUDES="--exclude=.git --exclude=node_modules --exclude=user-logs --exclude=*.log --exclude=.DS_Store --exclude=Thumbs.db --exclude=.vscode --exclude=.idea --exclude=*.swp --exclude=*.swo"
    
    # Try rsync with different SSH options
    rsync -avz --progress --delete $EXCLUDES \
        -e "sshpass -p '$PASSWORD' ssh -o StrictHostKeyChecking=no -o LogLevel=ERROR -o UserKnownHostsFile=/dev/null" \
        ./ "$USERNAME@$SERVER:$REMOTE_DIR/"
    
    if [ $? -eq 0 ]; then
        echo "Rsync transfer completed successfully"
    else
        echo "Rsync failed, trying alternative method..."
        
        # Alternative rsync without compression
        rsync -av --progress --delete $EXCLUDES \
            -e "sshpass -p '$PASSWORD' ssh -o StrictHostKeyChecking=no -o LogLevel=ERROR" \
            ./ "$USERNAME@$SERVER:$REMOTE_DIR/"
        
        if [ $? -eq 0 ]; then
            echo "Alternative rsync completed successfully"
        else
            echo "Rsync failed, falling back to scp..."
            USE_SCP=1
        fi
    fi
else
    echo "Rsync not available, using scp..."
    USE_SCP=1
fi

# Fallback to scp if rsync failed or not available
if [ "$USE_SCP" = "1" ]; then
    echo "Transferring files with scp (this may take longer)..."
    
    # Create temporary directory with filtered files
    TEMP_DIR=$(mktemp -d)
    
    # Copy files excluding common development files
    find . -type f \
        ! -path "./.git/*" \
        ! -path "./node_modules/*" \
        ! -path "./user-logs/*" \
        ! -name "*.log" \
        ! -name ".DS_Store" \
        ! -name "Thumbs.db" \
        ! -path "./.vscode/*" \
        ! -path "./.idea/*" \
        ! -name "*.swp" \
        ! -name "*.swo" \
        -exec cp --parents {} "$TEMP_DIR/" \;
    
    # Transfer filtered files
    sshpass -p "$PASSWORD" scp -o StrictHostKeyChecking=no -r "$TEMP_DIR"/* "$USERNAME@$SERVER:$REMOTE_DIR/"
    SCP_RESULT=$?
    
    # Clean up
    rm -rf "$TEMP_DIR"
    
    if [ $SCP_RESULT -ne 0 ]; then
        echo "Error: File transfer failed"
        exit 1
    fi
    
    echo "SCP transfer completed successfully"
fi

echo "Step 3: Setting up application on server..."
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$USERNAME@$SERVER" << EOF
    cd $REMOTE_DIR
    
    # Install Node.js if not present
    if ! command -v node &> /dev/null; then
        echo "Installing Node.js..."
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        sudo apt-get install -y nodejs
    fi
    
    # Install dependencies
    if [ -f "package.json" ]; then
        echo "Installing npm dependencies..."
        npm install
    fi
    
    # Install PM2 if not present
    if ! command -v pm2 &> /dev/null; then
        echo "Installing PM2..."
        npm install -g pm2
    fi
    
    # Stop existing process
    pm2 stop server.js 2>/dev/null || true
    pm2 delete server.js 2>/dev/null || true
    
    # Start application
    echo "Starting application..."
    PORT=$PORT pm2 start server.js --name "app-$PORT"
    pm2 save
    pm2 status
    
    echo "Application deployed successfully!"
EOF

if [ $? -ne 0 ]; then
    echo "Error: Failed to set up application on server"
    exit 1
fi

echo "Deployment completed successfully!"
echo "Your application is now running on $SERVER:$PORT"
echo "Admin interface: http://$SERVER:$PORT/admin"
