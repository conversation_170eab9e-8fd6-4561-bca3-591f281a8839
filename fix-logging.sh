#!/bin/bash

# <PERSON><PERSON>t to fix Winston logging issues on server
echo "🔧 Fixing Winston logging issues..."

# Check current working directory
echo "Current directory: $(pwd)"

# Check if we're in the right directory
if [ ! -f "server.js" ]; then
    echo "❌ server.js not found. Please run this script from the application directory."
    exit 1
fi

# Check log directory permissions
echo "📁 Checking log directory permissions..."
if [ -d "user-logs" ]; then
    echo "Log directory exists"
    ls -la user-logs/
    
    # Fix permissions if needed
    echo "🔧 Fixing permissions..."
    chmod 755 user-logs/
    chmod 644 user-logs/*.log 2>/dev/null || echo "No log files to fix permissions for"
    
    # Check disk space
    echo "💾 Checking disk space..."
    df -h .
    
    # Check if log file is writable
    echo "✍️  Testing log file write access..."
    if touch user-logs/test-write.tmp; then
        echo "✅ Log directory is writable"
        rm user-logs/test-write.tmp
    else
        echo "❌ Log directory is not writable"
        echo "Trying to fix ownership..."
        chown -R $(whoami):$(whoami) user-logs/ 2>/dev/null || echo "Could not change ownership"
    fi
else
    echo "📁 Creating log directory..."
    mkdir -p user-logs
    chmod 755 user-logs
fi

# Check if PM<PERSON> is running the app
echo "🔍 Checking PM2 status..."
if command -v pm2 &> /dev/null; then
    pm2 status
    
    echo "🔄 Restarting PM2 application..."
    pm2 restart app-8081 2>/dev/null || pm2 restart all
    
    echo "💾 Saving PM2 configuration..."
    pm2 save
    
    echo "📊 PM2 status after restart:"
    pm2 status
    
    echo "📝 Recent PM2 logs:"
    pm2 logs --lines 5
else
    echo "⚠️  PM2 not found. If using direct node, please restart manually."
fi

# Test logging by creating a test entry
echo "🧪 Testing logging functionality..."
node -e "
const fs = require('fs');
const path = require('path');

try {
    const testLog = {
        level: 'info',
        message: 'Test log entry from fix script',
        timestamp: new Date().toISOString(),
        test: true
    };
    
    const logLine = JSON.stringify(testLog) + '\n';
    fs.appendFileSync('user-logs/user-interactions.log', logLine);
    console.log('✅ Test log entry written successfully');
} catch (error) {
    console.error('❌ Failed to write test log:', error.message);
}
"

# Show recent log entries
echo "📋 Recent log entries:"
tail -5 user-logs/user-interactions.log 2>/dev/null || echo "No log file found or empty"

echo ""
echo "🎉 Fix script completed!"
echo ""
echo "📋 Next steps:"
echo "1. Check if new logs appear: tail -f user-logs/user-interactions.log"
echo "2. Test the application by visiting the webpage"
echo "3. Monitor PM2 logs: pm2 logs app-8081"
echo "4. If issues persist, check server.js Winston configuration"
