# Troubleshooting User Interaction Logging

## Issue: Logs working locally but not on deployed server

### Quick Diagnosis Steps

1. **Test API Endpoints**
   - Upload `test-api.html` to your server
   - Visit `http://your-server:8081/test-api.html`
   - Run through all the tests to see which ones fail

2. **Check Browser Console**
   - Open browser developer tools (F12)
   - Go to Console tab
   - Look for error messages when visiting the main page
   - The enhanced logging will show detailed error information

3. **Check Server Logs**
   - SSH into your server
   - Check if the Node.js process is running: `pm2 status`
   - View server logs: `pm2 logs app-8081`
   - Look for error messages or missing API calls

### Common Issues and Solutions

#### 1. **Port/Firewall Issues**
**Symptoms:** API test page shows network errors, can't connect to endpoints

**Solutions:**
- Ensure port 8081 is open in firewall: `sudo ufw allow 8081`
- Check if the application is running: `pm2 status`
- Verify the application is listening on the correct port: `netstat -tlnp | grep 8081`

#### 2. **CORS Issues**
**Symptoms:** Browser console shows CORS errors

**Solutions:**
- CORS is already enabled in the server
- If still having issues, check if you're accessing via HTTPS but server is HTTP
- Ensure you're accessing the correct domain/IP

#### 3. **File Path Issues**
**Symptoms:** Main page loads but API endpoints return 404

**Solutions:**
- Check if `server.js` is in the correct directory
- Verify the `staticDir` path in `server.js` points to the right location
- Ensure all files were uploaded correctly during deployment

#### 4. **Node.js/Dependencies Issues**
**Symptoms:** Server not starting, missing modules errors

**Solutions:**
```bash
# Reinstall dependencies
cd /var/www/bsod
npm install

# Restart the application
pm2 restart app-8081
```

#### 5. **JavaScript Errors**
**Symptoms:** Page loads but no interactions are logged, console shows JS errors

**Solutions:**
- Check browser console for JavaScript errors
- Ensure all JavaScript files loaded correctly
- Look for syntax errors or missing dependencies

### Debugging Commands

#### Check Server Status
```bash
# Check if PM2 process is running
pm2 status

# View application logs
pm2 logs app-8081

# Check system resources
pm2 monit

# Restart application
pm2 restart app-8081
```

#### Check Network Connectivity
```bash
# Test if port is accessible
curl http://localhost:8081/api/sessions

# Check if port is listening
netstat -tlnp | grep 8081

# Check firewall status
sudo ufw status
```

#### Check File Permissions
```bash
# Ensure correct ownership
sudo chown -R $USER:$USER /var/www/bsod

# Check file permissions
ls -la /var/www/bsod/
```

### Manual Testing

#### Test Session Creation
```bash
curl -X POST http://your-server:8081/api/session/create \
  -H "Content-Type: application/json"
```

#### Test Interaction Logging
```bash
curl -X POST http://your-server:8081/api/interaction/log \
  -H "Content-Type: application/json" \
  -d '{
    "sessionId": "test-session-123",
    "interaction": {
      "type": "test",
      "message": "Manual test"
    }
  }'
```

#### Check Logs
```bash
curl http://your-server:8081/admin/logs?limit=5
```

### Environment-Specific Issues

#### Different Domain/Port
If your server runs on a different port or domain:

1. **Check the deployment script** - ensure it's using the correct port
2. **Update firewall rules** - allow the correct port
3. **Check reverse proxy** - if using nginx/apache, ensure it's configured correctly

#### HTTPS vs HTTP
If your site uses HTTPS but the API uses HTTP:

1. **Enable HTTPS for the API** - configure SSL certificates
2. **Update the server configuration** - ensure it handles HTTPS requests
3. **Check mixed content policies** - browsers block HTTP requests from HTTPS pages

### Log Analysis

#### What to Look For in Server Logs
- Session creation messages: `"New session created"`
- Interaction logging: `"User interaction (batch)"`
- Error messages: Look for HTTP errors, database errors, etc.

#### What to Look For in Browser Console
- Network errors: Failed fetch requests
- JavaScript errors: Syntax errors, undefined variables
- CORS errors: Cross-origin request blocked
- Session initialization: `"User interaction logging initialized"`

### Recovery Steps

If logging is completely broken:

1. **Restart the server application**
   ```bash
   pm2 restart app-8081
   ```

2. **Clear browser cache and cookies**
   - Hard refresh: Ctrl+F5 or Cmd+Shift+R
   - Clear browser data for the site

3. **Redeploy the application**
   ```bash
   ./deploy.sh username password server.com
   ```

4. **Check system resources**
   ```bash
   df -h  # Check disk space
   free -h  # Check memory
   top  # Check CPU usage
   ```

### Getting Help

If you're still having issues:

1. **Collect diagnostic information:**
   - Browser console output
   - Server logs (`pm2 logs app-8081`)
   - Network tab in browser dev tools
   - Results from `test-api.html`

2. **Check the basics:**
   - Can you access the main page?
   - Can you access the admin interface?
   - Are there any error messages?

3. **Test with curl:**
   - Try the manual testing commands above
   - This helps isolate if it's a browser or server issue
