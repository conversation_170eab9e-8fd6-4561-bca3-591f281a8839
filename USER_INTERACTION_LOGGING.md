# User Interaction Logging System

## Overview

This system provides comprehensive logging of all user interactions on the BSOD page, allowing you to see exactly what users do from the moment they visit the page until they close it.

## Features

### 🔍 What Gets Logged

- **Page Events**: Load, unload, visibility changes, resize
- **Mouse Interactions**: Clicks, movements, enter/leave elements
- **Keyboard Events**: Key presses, key releases, modifier keys
- **Focus Events**: Element focus and blur
- **Scroll Events**: Page scrolling with coordinates
- **Fullscreen Events**: Enter/exit fullscreen mode
- **Custom Events**: Verification button clicks, escape attempts, audio events
- **Window Events**: Resize, before unload
- **Element Details**: Tag name, ID, class, text content, attributes

### 📊 Session Management

- Each user visit gets a unique session ID
- All interactions are associated with the session
- Session includes user agent, IP address, screen resolution
- Sessions are automatically created and managed

### 🎯 Specific BSOD Events Tracked

- **Verification Button**: When users click "Begin Verification"
- **Escape Key Attempts**: When users try to press Escape (blocked)
- **Fullscreen Events**: When fullscreen is entered/exited
- **Audio Events**: When error sounds are played
- **Window Animations**: When the fake Windows appear
- **Page Lock**: When the page becomes "locked"

## How to Use

### 1. Start the Server

```bash
node server.js
```

The server will start on port 8081 and create a `user-logs` directory for log files.

### 2. Access the Admin Interface

Open your browser and go to:
```
http://localhost:8081/admin
```

### 3. View Real-time Logs

The admin interface provides:
- **Active Sessions**: See all current user sessions
- **Interaction Logs**: Real-time log of all user interactions
- **Session Filtering**: Filter logs by specific session ID
- **Auto Refresh**: Automatically update logs every 2 seconds
- **Batch Limits**: Control how many log entries to display

### 4. Monitor User Behavior

You can see:
- When users first visit the page
- How they move their mouse
- When they click the verification button
- If they try to escape or close the page
- How long they stay on each part of the interface
- Their screen resolution and browser details

## API Endpoints

### Session Management
- `POST /api/session/create` - Create new session
- `GET /api/session/:sessionId` - Get session details
- `GET /api/sessions` - List all active sessions
- `POST /api/session/:sessionId/end` - End session

### Interaction Logging
- `POST /api/interaction/log` - Log single interaction
- `POST /api/interaction/batch` - Log multiple interactions

### Admin Interface
- `GET /admin` - Admin dashboard
- `GET /admin/logs` - Get logs (with filtering)

## Log File Structure

Logs are stored in `user-logs/user-interactions.log` as JSON lines:

```json
{
  "level": "info",
  "message": "User interaction (batch)",
  "type": "click",
  "element": {
    "tagName": "BUTTON",
    "id": "fullscreenButton",
    "className": "fullscreen-button",
    "textContent": "🛡️ Begin Verification"
  },
  "x": 895,
  "y": 603,
  "sessionId": "118afad6-13f3-4914-ad3d-d235378b23e7",
  "timestamp": "2025-07-18T18:52:34.509Z",
  "clientTimestamp": "2025-07-18T18:52:33.875Z"
}
```

## Configuration

### Server Configuration
- Port: 8081 (change in `server.js`)
- Log file location: `user-logs/` directory
- Log rotation: 10MB max file size, 5 files kept

### Client Configuration
- Batch size: 10 interactions per batch
- Flush interval: 2 seconds
- Mouse move throttle: 500ms (to avoid spam)

## Security Considerations

- The admin interface has no authentication (add if needed)
- Logs contain detailed user behavior data
- Consider GDPR/privacy implications
- IP addresses are logged
- User agents are logged

## Troubleshooting

### No Logs Appearing
1. Check if server is running on correct port
2. Verify `user-logs` directory exists
3. Check browser console for JavaScript errors
4. Ensure network requests to `/api/` endpoints work

### Admin Interface Not Loading
1. Verify server is running
2. Check if port 8081 is accessible
3. Look for server errors in console

### Missing Interactions
1. Check if session was created successfully
2. Verify JavaScript is enabled in browser
3. Check for network connectivity issues
4. Look for batch flushing in server logs

## Example Use Cases

1. **Security Analysis**: See if users try to escape or bypass the interface
2. **User Behavior**: Understand how users interact with the fake interface
3. **Performance Monitoring**: Track how long users spend on each step
4. **Debugging**: Identify issues with the interface or user flow
5. **Analytics**: Gather statistics on user engagement and behavior

## Log Analysis Tips

- Filter by session ID to follow a specific user's journey
- Look for escape key attempts to see bypass attempts
- Monitor fullscreen events to see if users try to exit
- Track mouse movements to understand user confusion or hesitation
- Check verification button clicks to see conversion rates
