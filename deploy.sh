#!/bin/bash

# Deploy script for Node.js application with PM2
# Uses rsync for efficient file synchronization
# Usage: ./deploy.sh <username> <password> <server>

# Check if correct number of arguments provided
if [ $# -ne 3 ]; then
    echo "Usage: $0 <username> <password> <server>"
    echo "Example: $0 myuser mypass example.com"
    exit 1
fi

USERNAME=$1
PASSWORD=$2
SERVER=$3
REMOTE_DIR="/var/www/bsod"
PORT=8081

echo "Starting deployment to $SERVER..."

# Check if required tools are installed
if ! command -v sshpass &> /dev/null; then
    echo "Error: sshpass is not installed. Please install it first."
    echo "Ubuntu/Debian: sudo apt-get install sshpass"
    echo "CentOS/RHEL: sudo yum install sshpass"
    echo "macOS: brew install sshpass"
    exit 1
fi

if ! command -v rsync &> /dev/null; then
    echo "Error: rsync is not installed. Please install it first."
    echo "Ubuntu/Debian: sudo apt-get install rsync"
    echo "CentOS/RHEL: sudo yum install rsync"
    echo "macOS: rsync is usually pre-installed"
    exit 1
fi

# Check if current directory contains server.js
if [ ! -f "server.js" ]; then
    echo "Warning: server.js not found in current directory"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo "Step 1: Creating remote directory..."
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$USERNAME@$SERVER" "mkdir -p $REMOTE_DIR"

if [ $? -ne 0 ]; then
    echo "Error: Failed to create remote directory or connect to server"
    exit 1
fi

echo "Step 2: Syncing files to server using rsync..."
# Create .rsync-exclude file if it doesn't exist
if [ ! -f ".rsync-exclude" ]; then
    cat > .rsync-exclude << 'EOL'
.git/
.gitignore
node_modules/
user-logs/
*.log
.DS_Store
Thumbs.db
.vscode/
.idea/
*.swp
*.swo
*~
EOL
    echo "Created .rsync-exclude file with common exclusions"
fi

# Try rsync first, fall back to scp if it fails
echo "Attempting rsync transfer..."
# Use rsync with SSH and password authentication
# -a: archive mode (preserves permissions, timestamps, etc.)
# -v: verbose output
# -z: compress data during transfer
# -h: human-readable output
# --progress: show progress during transfer
# --delete: delete files on destination that don't exist in source
# --exclude-from: exclude files listed in .rsync-exclude
rsync -avzh --progress --delete \
    --exclude-from=.rsync-exclude \
    -e "sshpass -p '$PASSWORD' ssh -o StrictHostKeyChecking=no -o LogLevel=QUIET" \
    ./ "$USERNAME@$SERVER:$REMOTE_DIR/"

# If rsync fails, fall back to scp
if [ $? -ne 0 ]; then
    echo "Rsync failed, falling back to scp..."
    echo "Note: This will transfer all files (slower than rsync)"

    # Remove files that should be excluded from scp transfer
    echo "Preparing files for scp transfer..."

    # Create a temporary directory with files to transfer
    TEMP_DIR=$(mktemp -d)

    # Copy all files except excluded ones
    rsync -a --exclude-from=.rsync-exclude ./ "$TEMP_DIR/"

    # Use scp to transfer the filtered files
    sshpass -p "$PASSWORD" scp -o StrictHostKeyChecking=no -r "$TEMP_DIR"/* "$USERNAME@$SERVER:$REMOTE_DIR/"
    SCP_RESULT=$?

    # Clean up temporary directory
    rm -rf "$TEMP_DIR"

    if [ $SCP_RESULT -ne 0 ]; then
        echo "Error: Both rsync and scp failed to transfer files"
        exit 1
    else
        echo "Files transferred successfully using scp"
    fi
else
    echo "Files synced successfully using rsync"
fi

echo "Step 3: Setting up Node.js and dependencies..."

# Step 3a: Check and install Node.js
echo "Step 3a: Checking Node.js installation..."
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=30 "$USERNAME@$SERVER" "
    cd $REMOTE_DIR
    if ! command -v node &> /dev/null; then
        echo 'Node.js not found, installing...'
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - 2>/dev/null
        sudo apt-get install -y nodejs 2>/dev/null || echo 'Node.js installation may have failed'
    else
        echo 'Node.js already installed:' \$(node --version)
    fi
"

if [ $? -ne 0 ]; then
    echo "Warning: Node.js setup had issues, continuing..."
fi

# Step 3b: Install dependencies
echo "Step 3b: Installing npm dependencies..."
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=30 "$USERNAME@$SERVER" "
    cd $REMOTE_DIR
    if [ -f 'package.json' ]; then
        echo 'Installing npm dependencies...'
        npm install --production 2>/dev/null || echo 'npm install may have failed'
    fi
"

if [ $? -ne 0 ]; then
    echo "Warning: npm install had issues, continuing..."
fi

# Step 3c: Install and setup PM2
echo "Step 3c: Setting up PM2..."
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=30 "$USERNAME@$SERVER" "
    cd $REMOTE_DIR
    if ! command -v pm2 &> /dev/null; then
        echo 'Installing PM2...'
        npm install -g pm2 2>/dev/null || echo 'PM2 installation may have failed'
    else
        echo 'PM2 already installed:' \$(pm2 --version)
    fi
"

if [ $? -ne 0 ]; then
    echo "Warning: PM2 setup had issues, continuing..."
fi

# Step 3d: Start the application
echo "Step 3d: Starting application..."
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=30 "$USERNAME@$SERVER" "
    cd $REMOTE_DIR

    # Stop existing processes
    pm2 stop server.js 2>/dev/null || true
    pm2 delete server.js 2>/dev/null || true

    # Start the application
    echo 'Starting application with PM2...'
    PORT=$PORT pm2 start server.js --name 'app-$PORT' 2>/dev/null || echo 'PM2 start may have failed'

    # Save PM2 configuration
    pm2 save 2>/dev/null || true

    # Show status
    pm2 status 2>/dev/null || echo 'Could not get PM2 status'

    echo 'Deployment process completed!'
"

if [ $? -ne 0 ]; then
    echo "Error: Failed to set up application on server"
    exit 1
fi

echo "Deployment completed successfully!"
echo "Your application is now running on $SERVER:$PORT"