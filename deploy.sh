#!/bin/bash

# Deploy script for Node.js application with PM2
# Usage: ./deploy.sh <username> <password> <server>

# Check if correct number of arguments provided
if [ $# -ne 3 ]; then
    echo "Usage: $0 <username> <password> <server>"
    echo "Example: $0 myuser mypass example.com"
    exit 1
fi

USERNAME=$1
PASSWORD=$2
SERVER=$3
REMOTE_DIR="/var/www/bsod"
PORT=8081

echo "Starting deployment to $SERVER..."

# Check if required tools are installed
if ! command -v sshpass &> /dev/null; then
    echo "Error: sshpass is not installed. Please install it first."
    echo "Ubuntu/Debian: sudo apt-get install sshpass"
    echo "CentOS/RHEL: sudo yum install sshpass"
    echo "macOS: brew install sshpass"
    exit 1
fi

if ! command -v rsync &> /dev/null; then
    echo "Error: rsync is not installed. Please install it first."
    echo "Ubuntu/Debian: sudo apt-get install rsync"
    echo "CentOS/RHEL: sudo yum install rsync"
    echo "macOS: rsync is usually pre-installed"
    exit 1
fi

# Check if current directory contains server.js
if [ ! -f "server.js" ]; then
    echo "Warning: server.js not found in current directory"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo "Step 1: Creating remote directory..."
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$USERNAME@$SERVER" "mkdir -p $REMOTE_DIR"

if [ $? -ne 0 ]; then
    echo "Error: Failed to create remote directory or connect to server"
    exit 1
fi

echo "Step 2: Syncing files to server using rsync..."
# Create .rsync-exclude file if it doesn't exist
if [ ! -f ".rsync-exclude" ]; then
    cat > .rsync-exclude << 'EOL'
.git/
.gitignore
node_modules/
user-logs/
*.log
.DS_Store
Thumbs.db
.vscode/
.idea/
*.swp
*.swo
*~
EOL
    echo "Created .rsync-exclude file with common exclusions"
fi

# Use rsync with SSH and password authentication
# -a: archive mode (preserves permissions, timestamps, etc.)
# -v: verbose output
# -z: compress data during transfer
# -h: human-readable output
# --progress: show progress during transfer
# --delete: delete files on destination that don't exist in source
# --exclude-from: exclude files listed in .rsync-exclude
RSYNC_PASSWORD="$PASSWORD" rsync -avzh --progress --delete \
    --exclude-from=.rsync-exclude \
    -e "sshpass -e ssh -o StrictHostKeyChecking=no" \
    ./ "$USERNAME@$SERVER:$REMOTE_DIR/"

if [ $? -ne 0 ]; then
    echo "Error: Failed to sync files to server"
    exit 1
fi

echo "Step 3: Installing system dependencies, Node.js, and setting up PM2..."
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$USERNAME@$SERVER" << EOF
    cd $REMOTE_DIR
    
    # Function to detect OS
    detect_os() {
        if [ -f /etc/os-release ]; then
            . /etc/os-release
            echo \$ID
        elif [ -f /etc/redhat-release ]; then
            echo "rhel"
        elif [ -f /etc/debian_version ]; then
            echo "debian"
        else
            echo "unknown"
        fi
    }
    
    OS=\$(detect_os)
    echo "Detected OS: \$OS"
    
    # Install curl if not present (needed for Node.js installation)
    if ! command -v curl &> /dev/null; then
        echo "Installing curl..."
        case \$OS in
            ubuntu|debian)
                sudo apt-get update
                sudo apt-get install -y curl
                ;;
            centos|rhel|fedora)
                sudo yum install -y curl || sudo dnf install -y curl
                ;;
            *)
                echo "Warning: Unknown OS. Please install curl manually if needed."
                ;;
        esac
    fi
    
    # Install Node.js if not present
    if ! command -v node &> /dev/null; then
        echo "Installing Node.js..."
        case \$OS in
            ubuntu|debian)
                # Install Node.js 18.x LTS
                curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
                sudo apt-get install -y nodejs
                ;;
            centos|rhel|fedora)
                # Install Node.js 18.x LTS
                curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
                sudo yum install -y nodejs || sudo dnf install -y nodejs
                ;;
            *)
                echo "Error: Cannot auto-install Node.js on this OS. Please install Node.js manually."
                exit 1
                ;;
        esac
    else
        echo "Node.js is already installed: \$(node --version)"
    fi
    
    # Verify Node.js installation
    if ! command -v node &> /dev/null; then
        echo "Error: Node.js installation failed"
        exit 1
    fi
    
    # Install npm dependencies if package.json exists
    if [ -f "package.json" ]; then
        echo "Installing npm dependencies..."
        npm install
    fi
    
    # Install PM2 globally if not already installed
    if ! command -v pm2 &> /dev/null; then
        echo "Installing PM2..."
        npm install -g pm2
    else
        echo "PM2 is already installed: \$(pm2 --version)"
    fi
    
    # Stop existing PM2 process if running
    pm2 stop server.js 2>/dev/null || true
    pm2 delete server.js 2>/dev/null || true
    
    # Start the application with PM2
    echo "Starting application with PM2 on port $PORT..."
    PORT=$PORT pm2 start server.js --name "app-$PORT"
    
    # Save PM2 configuration
    pm2 save
    
    # Show PM2 status
    pm2 status
    
    echo "Application deployed successfully!"
    echo "Access your application at: http://$SERVER:$PORT"
EOF

if [ $? -ne 0 ]; then
    echo "Error: Failed to set up application on server"
    exit 1
fi

echo "Deployment completed successfully!"
echo "Your application is now running on $SERVER:$PORT"