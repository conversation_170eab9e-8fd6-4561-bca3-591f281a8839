# Winston Logging Fix for Server

## Problem
- PM2 logs show user interactions correctly
- Log file (`user-logs/user-interactions.log`) stops updating
- <PERSON> file transport becomes disconnected

## Root Causes
1. **File permissions issues**
2. **Winston file transport getting stuck**
3. **File handle issues in production**
4. **Disk space or inode issues**

## Quick Fix (Run on Server)

### Step 1: Run the Fix Script
```bash
cd /var/www/bsod
chmod +x fix-logging.sh
./fix-logging.sh
```

### Step 2: Test Logging
```bash
node check-logging.js
```

### Step 3: Monitor Logs
```bash
# In one terminal - watch the log file
tail -f user-logs/user-interactions.log

# In another terminal - watch PM2 logs
pm2 logs app-8081
```

### Step 4: Test the Application
1. Open the webpage in browser
2. Interact with the page (move mouse, press keys)
3. Check if both PM2 logs AND log file show new entries

## Manual Fix Steps

### 1. Check File Permissions
```bash
ls -la user-logs/
chmod 755 user-logs/
chmod 644 user-logs/*.log
```

### 2. Check Disk Space
```bash
df -h
du -sh user-logs/
```

### 3. Restart PM2 Application
```bash
pm2 restart app-8081
pm2 save
```

### 4. Test Write Access
```bash
echo '{"test": true, "timestamp": "'$(date -Iseconds)'"}' >> user-logs/user-interactions.log
```

## Code Changes Made

### Enhanced Winston Configuration
- Added error handling for file transport
- Added automatic transport recreation on failure
- Added backup logging mechanism
- Added `exitOnError: false` for resilience

### Key Improvements
1. **Error Recovery**: Automatically recreates file transport on error
2. **Backup Logging**: Falls back to direct file write if Winston fails
3. **Better Error Handling**: Logs errors without crashing the app

## Verification Steps

### 1. Check Current Status
```bash
# Check if logs are being written
tail -5 user-logs/user-interactions.log

# Check PM2 status
pm2 status

# Check recent PM2 logs
pm2 logs --lines 10
```

### 2. Test Real-time Logging
```bash
# Start monitoring log file
tail -f user-logs/user-interactions.log &

# Open webpage and interact
# You should see new entries appear in real-time
```

### 3. Verify Analytics Work
```bash
# Test analytics endpoint
curl http://localhost:8081/api/sys/analytics

# Should return recent session data
```

## Common Issues & Solutions

### Issue 1: Permission Denied
```bash
sudo chown -R www-data:www-data user-logs/
# or
sudo chown -R $(whoami):$(whoami) user-logs/
```

### Issue 2: Disk Full
```bash
df -h
# Clean up old logs if needed
find user-logs/ -name "*.log.*" -mtime +7 -delete
```

### Issue 3: Winston Module Issues
```bash
npm install winston --save
pm2 restart app-8081
```

### Issue 4: Multiple Node Processes
```bash
# Kill all node processes
pkill -f "node.*server.js"
# Restart with PM2
pm2 start server.js --name app-8081
```

## Monitoring Commands

### Real-time Monitoring
```bash
# Terminal 1: Watch log file
tail -f user-logs/user-interactions.log

# Terminal 2: Watch PM2 logs  
pm2 logs app-8081 --lines 0

# Terminal 3: Test application
curl http://localhost:8081/
```

### Check Log File Status
```bash
# File size and modification time
ls -lah user-logs/user-interactions.log

# Count log entries
wc -l user-logs/user-interactions.log

# Last 10 entries with timestamps
tail -10 user-logs/user-interactions.log | jq -r '.timestamp + " " + .message'
```

## Prevention

### 1. Log Rotation
The Winston configuration includes:
- `maxsize: 10485760` (10MB per file)
- `maxFiles: 5` (keep 5 old files)

### 2. Error Monitoring
The enhanced logger includes:
- Automatic error detection
- Transport recreation on failure
- Backup logging mechanism

### 3. Health Checks
Add to your monitoring:
```bash
# Check if log file is being updated (should be < 60 seconds old)
find user-logs/ -name "user-interactions.log" -mmin -1
```

## Success Indicators

✅ **Working Correctly When:**
1. PM2 logs show user interactions
2. Log file shows same interactions with matching timestamps
3. `tail -f user-logs/user-interactions.log` shows new entries in real-time
4. Analytics endpoint returns recent data
5. Dashboard shows current session statistics

❌ **Still Broken If:**
1. PM2 logs show interactions but log file doesn't update
2. Log file timestamp is old (> 1 hour)
3. Analytics endpoint returns empty or old data
4. Dashboard shows no recent activity

## Emergency Fallback

If Winston continues to fail, you can temporarily use direct file logging:

```javascript
// Add to server.js as emergency fallback
const emergencyLog = (data) => {
  const logEntry = JSON.stringify({
    ...data,
    timestamp: new Date().toISOString(),
    level: 'info'
  }) + '\n';
  
  require('fs').appendFileSync('user-logs/user-interactions.log', logEntry);
};
```
