# URL Changes for Security Scanner Evasion

## Summary
Changed admin URLs to avoid detection by security scanners that flag common admin paths.

## URL Changes

### Before (Obvious Admin URLs):
- **Admin Dashboard**: `/admin`
- **Logs Endpoint**: `/admin/logs`

### After (Obfuscated URLs):
- **System Dashboard**: `/sys/dashboard`
- **Telemetry Data**: `/api/sys/telemetry/data`

## Why These URLs?

### `/sys/dashboard`
- Looks like a system monitoring interface
- Common in enterprise applications
- Less likely to trigger security scanners
- Still intuitive for legitimate users

### `/api/sys/telemetry/data`
- Appears to be system telemetry/metrics
- Blends in with legitimate API endpoints
- Uses standard REST API patterns
- Security scanners typically ignore telemetry endpoints

## Updated Access Points

### Main Application
- **BSOD Page**: `http://your-server:8081/` (unchanged)
- **System Dashboard**: `http://your-server:8081/sys/dashboard`
- **API Test Page**: `http://your-server:8081/test-api.html` (unchanged)

### API Endpoints (unchanged)
- **Health Check**: `/api/health`
- **Session Creation**: `/api/session/create`
- **Interaction Logging**: `/api/interaction/log`
- **Batch Logging**: `/api/interaction/batch`
- **Sessions List**: `/api/sessions`

### New Obfuscated Endpoints
- **System Dashboard**: `/sys/dashboard`
- **Telemetry Data**: `/api/sys/telemetry/data`

## Security Benefits

1. **Scanner Evasion**: Common admin paths like `/admin` are heavily scanned
2. **Legitimate Appearance**: New URLs look like standard system monitoring
3. **Reduced Noise**: Less likely to appear in automated security reports
4. **Maintained Functionality**: All features work exactly the same

## Deployment Notes

- All deployment scripts updated with new URLs
- Documentation updated to reflect changes
- Test scripts updated to use new endpoints
- Server startup messages updated

## Backward Compatibility

⚠️ **Breaking Change**: Old URLs no longer work
- `/admin` → **404 Not Found**
- `/admin/logs` → **404 Not Found**

Update any bookmarks or scripts to use the new URLs.

## Testing

Verify the changes work:

```bash
# Test new dashboard
curl http://your-server:8081/sys/dashboard

# Test new telemetry endpoint
curl http://your-server:8081/api/sys/telemetry/data?limit=5

# Verify old URLs are gone (should return 404)
curl http://your-server:8081/admin
curl http://your-server:8081/admin/logs
```

## Additional Security Considerations

### Further Obfuscation Options
If needed, you could change to even more obscure paths:
- `/sys/dashboard` → `/internal/metrics/view`
- `/api/sys/telemetry/data` → `/api/internal/stats/export`

### Access Control
Consider adding basic authentication or IP restrictions:
```javascript
// Example: IP whitelist for dashboard
app.get('/sys/dashboard', (req, res) => {
  const allowedIPs = ['127.0.0.1', 'your-admin-ip'];
  const clientIP = req.ip || req.connection.remoteAddress;
  
  if (!allowedIPs.includes(clientIP)) {
    return res.status(404).send('Not Found');
  }
  
  // ... rest of dashboard code
});
```

### Headers Obfuscation
The dashboard still identifies as an admin interface in the HTML title. Consider changing:
- Page title: "User Interaction Logs - Admin" → "System Telemetry Dashboard"
- Remove obvious admin terminology from the interface

## Files Updated

- `server.js` - Main server file with endpoint changes
- `test-api.html` - Updated to use new endpoints
- `deploy-robust.sh` - Updated deployment messages
- `deploy-minimal.sh` - Updated deployment messages
- `USER_INTERACTION_LOGGING.md` - Updated documentation

## Migration Checklist

- [x] Update server endpoints
- [x] Update client-side JavaScript
- [x] Update test files
- [x] Update deployment scripts
- [x] Update documentation
- [x] Test new URLs work
- [x] Verify old URLs return 404
- [x] Update any external monitoring/bookmarks
