# Nginx 502 Bad Gateway Fix

## Problem
You're getting a 502 Bad Gateway error from nginx, which means:
- Nginx is running and receiving requests
- <PERSON><PERSON><PERSON> is configured to proxy to your Node.js app
- But nginx can't connect to your Node.js application

## Diagnosis Steps

### 1. Check if your Node.js app is running
```bash
# Check if the process is running
ps aux | grep node
pm2 status
pm2 logs

# Check if port 8081 is listening
netstat -tlnp | grep 8081
ss -tlnp | grep 8081
```

### 2. Check nginx configuration
```bash
# Check nginx status
sudo systemctl status nginx

# Check nginx configuration
sudo nginx -t

# View nginx error logs
sudo tail -f /var/log/nginx/error.log

# Check your site configuration
sudo cat /etc/nginx/sites-available/default
# or
sudo cat /etc/nginx/sites-available/your-site-name
```

### 3. Test direct connection to Node.js
```bash
# Test if Node.js app responds directly
curl http://localhost:8081/api/health
curl http://127.0.0.1:8081/api/health

# If above fails, your Node.js app isn't running properly
```

## Common Fixes

### Fix 1: Start/Restart your Node.js application
```bash
# If using PM2
pm2 restart all
pm2 start server.js --name app-8081

# If using direct node
cd /var/www/bsod
node server.js &

# If using systemd service
sudo systemctl restart your-app-name
```

### Fix 2: Fix nginx proxy configuration
Create or update nginx configuration:

```bash
sudo nano /etc/nginx/sites-available/default
```

Add this configuration:
```nginx
server {
    listen 80;
    server_name your-domain.com;  # Replace with your domain or IP

    location / {
        proxy_pass http://127.0.0.1:8081;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

Then restart nginx:
```bash
sudo nginx -t  # Test configuration
sudo systemctl reload nginx
```

### Fix 3: Check firewall and ports
```bash
# Check if port 8081 is blocked
sudo ufw status
sudo iptables -L

# Allow port 8081 if needed
sudo ufw allow 8081
```

### Fix 4: Fix application binding
Make sure your Node.js app binds to the correct interface.

In your `server.js`, change:
```javascript
app.listen(PORT, () => {
```

To:
```javascript
app.listen(PORT, '0.0.0.0', () => {
```

Or specifically for localhost:
```javascript
app.listen(PORT, '127.0.0.1', () => {
```

## Quick Diagnostic Script

Save this as `diagnose.sh` and run it:
```bash
#!/bin/bash
echo "=== Node.js Process Check ==="
ps aux | grep node

echo -e "\n=== PM2 Status ==="
pm2 status 2>/dev/null || echo "PM2 not running or not installed"

echo -e "\n=== Port 8081 Check ==="
netstat -tlnp | grep 8081 || echo "Port 8081 not listening"

echo -e "\n=== Direct App Test ==="
curl -s http://localhost:8081/api/health || echo "App not responding on localhost:8081"

echo -e "\n=== Nginx Status ==="
sudo systemctl status nginx --no-pager -l

echo -e "\n=== Nginx Error Logs (last 10 lines) ==="
sudo tail -10 /var/log/nginx/error.log

echo -e "\n=== Nginx Configuration Test ==="
sudo nginx -t
```

## Step-by-Step Fix

1. **SSH into your server**
2. **Check if your app is running:**
   ```bash
   pm2 status
   ```

3. **If app is not running, start it:**
   ```bash
   cd /var/www/bsod
   pm2 start server.js --name app-8081
   ```

4. **Test direct connection:**
   ```bash
   curl http://localhost:8081/api/health
   ```

5. **If that works, check nginx config:**
   ```bash
   sudo nginx -t
   sudo systemctl reload nginx
   ```

6. **Check nginx error logs:**
   ```bash
   sudo tail -f /var/log/nginx/error.log
   ```

## Alternative: Bypass nginx temporarily

If you want to test without nginx:
1. **Stop nginx temporarily:**
   ```bash
   sudo systemctl stop nginx
   ```

2. **Change your app to listen on port 80:**
   ```bash
   # Edit server.js to use port 80
   sudo node server.js
   ```

3. **Access directly:**
   ```
   http://your-server-ip/admin
   ```

## Most Likely Solutions

Based on the error, try these in order:

1. **Restart your Node.js app:**
   ```bash
   pm2 restart all
   ```

2. **Check if it's running:**
   ```bash
   pm2 status
   curl http://localhost:8081/api/health
   ```

3. **If not running, start it:**
   ```bash
   cd /var/www/bsod
   pm2 start server.js --name app-8081
   ```

4. **Reload nginx:**
   ```bash
   sudo systemctl reload nginx
   ```

The 502 error almost always means your Node.js application isn't running or isn't accessible to nginx.
